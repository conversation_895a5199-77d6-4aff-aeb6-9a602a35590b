/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header Navigation */
/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

header nav ul li a.active {
    font-weight: bold;
    color: #007BFF;
}


/* Main Report Container */
    .report-container {
        max-width: 1400px;
        margin: 0 auto;
        flex: 1;
        margin-bottom: 20px;
        width: 80%;
    }

    .report-title {
        text-align: center;
        margin-bottom: 40px;
        font-size: 36px;
        font-weight: 700;
        color: #fff;
        text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
    }

    /* Main content styling */
    .report-content {
        background: #ffffff;
        border-radius: 24px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        position: relative;
        padding: 40px;
        color: #333;
    }

/* Patient Information Container */
.patient-info-container {
  background-color: #fafafa;
  border-radius: 12px;
  padding: 50px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

/* Patient Information Section */
.patient-info-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  padding: 0;
  border-bottom: none;
}

.patient-details p {
  margin: 10px 0;
  font-size: 17px;
}

.patient-details strong {
  color: #333;
  line-height: 1.5;
  display: inline-block;
  width: 120px;
}

/* Diagnosis Section */
.diagnosis-section {
  text-align: center;
}

.diagnosis-section h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 20px;
}

.scan-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.scan-image {
  max-width: 300px;
  width: 100%;
  height: auto;
  border-radius: 12px;
  border: 3px solid #e0e0e0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.scan-image:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.scan-image.zoomed {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1.5);
  z-index: 1000;
  max-width: 80vw;
  max-height: 80vh;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Analysis Section */
.analysis-section {
  padding: 40px;
  background: #fafafa;
}

.analysis-section h3 {
  font-size: 22px;
  color: #333;
  margin-bottom: 25px;
  border-bottom: 2px solid #1976d2;
  padding-bottom: 10px;
}

.condition-info p {
  margin: 15px 0;
  font-size: 16px;
  line-height: 1.7;
}

.condition-info strong {
  color: #1976d2;
}

.condition-info em {
  color: #d32f2f;
  font-weight: 600;
}

/* Condition Result Highlighting */
.condition-positive {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid #ffcdd2;
}

.condition-negative {
  background-color: #e8f5e8;
  color: #2e7d32;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid #c8e6c9;
}

/* Type Result Highlighting */
.type-positive {
  background-color: #f3e5f5;
  color: #7b1fa2;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid #e1bee7;
}

.type-negative {
  background-color: #e8eaf6;
  color: #3949ab;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid #c5cae9;
}

/* Multiple Tumor Types Display */
.multiple-tumor-types {
  margin-top: 8px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.tumor-type-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tumor-type-item:last-child {
  margin-bottom: 0;
}

.tumor-number {
  font-weight: bold;
  color: #495057;
  margin-right: 8px;
  min-width: 24px;
  font-size: 14px;
}

.tumor-type-item .type-positive {
  margin-left: 4px;
  font-size: 14px;
}

/* Confidence Score Highlighting */
.confidence-score {
  background-color: #e0f2f1;
  color: #00796b;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  border: 1px solid #b2dfdb;
}

/* Symptoms Section */
.symptoms-section {
  margin-top: 30px;
  padding: 25px;
  background: white;
  border-radius: 10px;
  border-left: 4px solid #ff9800;
}

.symptoms-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 18px;
}

.symptoms-list {
  list-style: none;
  padding: 0;
}

.symptoms-list li {
  padding: 8px 0;
  padding-left: 25px;
  position: relative;
  color: #555;
}

.symptoms-list li:before {
  content: "•";
  color: #ff9800;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.medical-note {
  margin-top: 20px;
  padding: 15px;
  background: #fff3e0;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.medical-note p {
  color: #e65100;
  font-size: 14px;
  margin: 0;
}

/* Technical Details */
.technical-details {
  padding: 20px 40px;
  border-top: 1px solid #e0e0e0;
}

.technical-details details {
  cursor: pointer;
}

.technical-details summary {
  font-weight: 600;
  color: #666;
  padding: 10px 0;
  outline: none;
}

.tech-info {
  padding: 15px 0;
  color: #666;
  font-size: 14px;
}

.tech-info p {
  margin: 5px 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding: 30px;
  background: #f8f9fa;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-primary:hover {
  background: #1565c0;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #757575;
  color: white;
}

.btn-secondary:hover {
  background: #616161;
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: #1976d2;
  border: 2px solid #1976d2;
}

.btn-outline:hover {
  background: #1976d2;
  color: white;
  transform: translateY(-2px);
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    width: 103.9%;
    margin: 0;
    margin-top: 50px;
    position: relative;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}



/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .navbar nav {
    gap: 20px;
  }

  .patient-info-section {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .report-title {
    font-size: 24px;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .scan-image {
    max-width: 250px;
  }
}

@media (max-width: 480px) {
  .report-container {
    margin: 15px auto;
    padding: 0 15px;
  }

  .patient-info-section,
  .analysis-section {
    padding: 25px;
  }

  .patient-details strong {
    width: 100px;
    font-size: 14px;
  }
}


    .image-source {
      font-size: 0.8em;
      color: #666;
      text-align: center;
      margin-top: 5px;
      font-style: italic;
    }

    /* Image Carousel Styles */
    .image-carousel-container {
      margin-top: 20px;
      background: #f8f9fa;
      border-radius: 15px;
      padding: 20px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .carousel-header {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 20px;
    }

    .image-counter {
      background: #1976d2;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
    }

    .image-carousel {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 0;
    }

    .carousel-btn {
      background: #1976d2;
      color: white;
      border: none;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
      z-index: 5;
    }

    .carousel-btn:hover {
      background: #1565c0;
      transform: scale(1.1);
    }

    .carousel-btn:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .carousel-btn-prev {
      margin-right: 20px;
    }

    .carousel-btn-next {
      margin-left: 20px;
    }

    .carousel-image-container {
      position: relative;
      max-width: 400px;
      width: 100%;
    }

    .carousel-slide {
      display: none;
      text-align: center;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;
    }

    .carousel-slide.active {
      display: block;
      opacity: 1;
    }

    .carousel-image {
      width: 100%;
      max-width: 400px;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2);
      cursor: pointer;
      transition: transform 0.3s ease;
    }

    .carousel-image:hover {
      transform: scale(1.02);
    }

    .carousel-image-details {
      margin-top: 15px;
      background: white;
      border-radius: 10px;
      padding: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .detail-row:last-child {
      margin-bottom: 0;
    }

    .detail-label {
      font-weight: 600;
      color: #555;
    }

    .detail-value {
      font-weight: 500;
    }

    .detail-value.positive {
      color: #d32f2f;
      font-weight: 600;
    }

    .detail-value.negative {
      color: #1976d2;
      font-weight: 600;
    }

    .detail-value.confidence {
      background: #e3f2fd;
      color: #1976d2;
      padding: 4px 8px;
      border-radius: 12px;
      font-weight: 600;
    }



    .scan-result-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    }

    .gallery-image {
      width: 100%;
      max-width: 250px;
      height: auto;
      border-radius: 8px;
      cursor: pointer;
      transition: transform 0.2s ease;
    }

    .gallery-image:hover {
      transform: scale(1.05);
    }

    .result-overlay {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .result-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      font-weight: bold;
      text-align: center;
    }

    .result-badge.positive {
      background: #dc3545;
      color: white;
    }

    .result-badge.negative {
      background: #28a745;
      color: white;
    }

    .confidence-badge {
      background: #007bff;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 0.8em;
      font-weight: bold;
      text-align: center;
    }

    .tumor-location {
      background: #ff6b35;
      color: white;
      padding: 4px 12px;
      border-radius: 15px;
      font-weight: 600;
      font-size: 0.9em;
      text-transform: capitalize;
      display: inline-block;
      margin-left: 5px;
    }

    .image-details {
      margin-top: 10px;
      font-size: 0.9em;
    }

    .image-details p {
      margin: 5px 0;
    }

    /* Modal for enlarged images */
    .image-modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.8);
      cursor: pointer;
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 90%;
      border-radius: 8px;
    }

    .close-modal {
      position: absolute;
      top: 15px;
      right: 25px;
      color: white;
      font-size: 35px;
      font-weight: bold;
      cursor: pointer;
    }

    @media (max-width: 768px) {
      .carousel-btn {
        width: 40px;
        height: 40px;
      }

      .carousel-btn-prev {
        margin-right: 15px;
      }

      .carousel-btn-next {
        margin-left: 15px;
      }

      .carousel-image-container {
        max-width: 300px;
      }
    }

    /* Action Buttons Styling */
    .action-buttons {
      margin-top: 30px;
      padding: 20px;
      text-align: center;
      background: #9c9a9a;
      border-radius: 15px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .btn {
      display: inline-block;
      padding: 12px 24px;
      margin: 0 10px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }

    .btn-primary {
      background: #007BFF;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    }

    .btn-outline {
      background: transparent;
      color: white;
      border: 2px solid white;
    }

    .btn-outline:hover {
      background: white;
      color: #1B1D43;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }

    @media (max-width: 768px) {
      .action-buttons {
        margin-top: 20px;
        padding: 15px;
      }

      .btn {
        display: block;
        margin: 10px auto;
        width: 80%;
        max-width: 250px;
      }
    }