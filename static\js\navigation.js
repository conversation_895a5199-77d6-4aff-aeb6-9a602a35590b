// Universal Navigation Highlighting Script
// This script makes the current page navigation link bold/active

document.addEventListener('DOMContentLoaded', function() {
    // Get current page path
    const currentPath = window.location.pathname;
    
    // Get all navigation links
    const navLinks = document.querySelectorAll('header nav ul li a, header nav a');
    
    // Remove active class from all links first
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // Add active class to current page link
    navLinks.forEach(link => {
        const linkPath = new URL(link.href).pathname;
        const linkText = link.textContent.trim().toLowerCase();

        // Skip "How It Works" links - never make them active/bold
        if (linkText === 'how it works') {
            return;
        }

        // Handle exact matches
        if (linkPath === currentPath) {
            link.classList.add('active');
        }

        // Handle special cases for root and home
        if (currentPath === '/' && (linkPath === '/' || linkText === 'home')) {
            link.classList.add('active');
        }

        // Handle dashboard/overview compatibility
        if ((currentPath === '/dashboard' || currentPath === '/overview') &&
            (linkPath === '/dashboard' || linkPath === '/overview' ||
             linkText === 'dashboard')) {
            link.classList.add('active');
        }
    });
    
    // Add CSS for active navigation links if not already present
    if (!document.querySelector('#nav-active-styles')) {
        const style = document.createElement('style');
        style.id = 'nav-active-styles';
        style.textContent = `
            header nav ul li a.active,
            header nav a.active {
                font-weight: bold !important;
                color: #1B1D43 !important;
            }

            header nav ul li a.active:hover,
            header nav a.active:hover {
                color: #000000 !important;
            }
        `;
        document.head.appendChild(style);
    }
});

// Function to manually set active navigation (useful for dynamic content)
function setActiveNavigation(pageName) {
    const navLinks = document.querySelectorAll('header nav ul li a, header nav a');

    navLinks.forEach(link => {
        link.classList.remove('active');
        const linkText = link.textContent.trim().toLowerCase();

        // Skip "How It Works" links - never make them active/bold
        if (linkText === 'how it works') {
            return;
        }

        if (linkText === pageName.toLowerCase()) {
            link.classList.add('active');
        }
    });
}
