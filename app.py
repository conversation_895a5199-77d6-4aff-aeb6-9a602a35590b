from flask import Flask, render_template, request, redirect, url_for, jsonify, flash, send_from_directory, session, send_file
from ultralytics import YOLO
from datetime import datetime
import csv
import os, cv2
import mysql.connector
from mysql.connector import <PERSON>rror
import json
from werkzeug.utils import secure_filename
import time
import uuid

app = Flask(__name__)
app.secret_key = 'radiolens_secret_key_2024'  # For flash messages

# Database Configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'patient_info',
    'charset': 'utf8mb4',
    'autocommit': True
}

# File Upload Configuration
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'pdf'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

# Database Connection Functions
def get_db_connection():
    """Get database connection"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except mysql.connector.Error as e:
        # Database connection error
        return None

def is_image_file(filename):
    """Check if file is an image based on extension"""
    if not filename:
        return False
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'}

def extract_patient_id_from_filename(filename):
    """Extract patient ID from filename using various patterns"""
    # Attempting to extract patient ID from filename

    # Remove file extension for easier processing
    name_without_ext = os.path.splitext(filename)[0]

    # Pattern 1: P0000 format (most common)
    import re

    # Look for P followed by digits (P0000, P0001, etc.)
    pattern1 = re.search(r'P\d{4,}', name_without_ext, re.IGNORECASE)
    if pattern1:
        patient_id = pattern1.group().upper()
        # Found patient ID using pattern P####
        return patient_id

    # Pattern 2: Tr-gl_0000 or Tr-pi_0000 format - extract the number part and convert to P format
    pattern2 = re.search(r'Tr-[a-z]+_(\d{4,})', name_without_ext, re.IGNORECASE)
    if pattern2:
        number = pattern2.group(1)
        patient_id = f"P{number}"
        # Found patient ID using Tr-* pattern
        return patient_id

    # Pattern 3: Look for any sequence that might be a patient ID
    # Split by common delimiters and look for P+digits pattern
    delimiters = ['_', '-', ' ', '.']
    parts = [name_without_ext]

    for delimiter in delimiters:
        new_parts = []
        for part in parts:
            new_parts.extend(part.split(delimiter))
        parts = new_parts

    for part in parts:
        if part.upper().startswith('P') and len(part) >= 5:
            # Check if the rest are digits
            if part[1:].isdigit():
                patient_id = part.upper()
                # Found patient ID using delimiter split
                return patient_id

    # Could not extract patient ID from filename
    return None

def create_tables_if_not_exist():
    """Create database tables if they don't exist"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()

        # Create patients table (matching Upload.sql exactly)
        patients_table = """
        CREATE TABLE IF NOT EXISTS patients (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id VARCHAR(50) NOT NULL,
            patient_name VARCHAR(100) NOT NULL,
            gender ENUM('male','female','other') NOT NULL,
            age INT(3) NOT NULL,
            date_of_birth DATE NOT NULL,
            radiologist VARCHAR(100) NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY patient_id (patient_id),
            KEY idx_patient_id (patient_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        cursor.execute(patients_table)

        # Create uploads table (matching Upload.sql exactly)
        uploads_table = """
        CREATE TABLE IF NOT EXISTS uploads (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id VARCHAR(50) NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            file_path VARCHAR(500) NOT NULL,
            file_size INT(11) NOT NULL,
            file_type VARCHAR(50) NOT NULL,
            scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_patient_id (patient_id),
            KEY fk_uploads_patient (patient_id),
            CONSTRAINT fk_uploads_patient FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        cursor.execute(uploads_table)

        # Create images_result table for YOLO analysis results (matching images_result.sql)
        results_table = """
        CREATE TABLE IF NOT EXISTS images_result (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id VARCHAR(50) NOT NULL,
            patient_name VARCHAR(100) NOT NULL,
            result_image VARCHAR(255) NOT NULL,
            predicted_class VARCHAR(100) NOT NULL,
            confidence DECIMAL(5,4) NOT NULL,
            x1 INT(11) NOT NULL,
            y1 INT(11) NOT NULL,
            x2 INT(11) NOT NULL,
            y2 INT(11) NOT NULL,
            width INT(11) NOT NULL,
            height INT(11) NOT NULL,
            mask_area INT(11) NOT NULL,

            scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            original_image VARCHAR(255) DEFAULT NULL,
            processing_time DECIMAL(6,3) DEFAULT NULL,
            PRIMARY KEY (id),
            KEY idx_patient_id (patient_id),
            KEY idx_predicted_class (predicted_class),
            KEY idx_scan_date (scan_date),
            KEY fk_images_result_patient (patient_id),
            CONSTRAINT fk_images_result_patient FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        cursor.execute(results_table)

        # Create user accounts table for authentication
        useracc_table = """
        CREATE TABLE IF NOT EXISTS useracc_data (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            email VARCHAR(100) NOT NULL,
            user_id VARCHAR(50) NOT NULL,
            password VARCHAR(255) NOT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            status ENUM('active','inactive') NOT NULL DEFAULT 'active',
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id),
            UNIQUE KEY email (email),
            KEY idx_user_id (user_id),
            KEY idx_email (email)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """
        cursor.execute(useracc_table)

        # Insert default admin account if it doesn't exist
        cursor.execute("SELECT COUNT(*) FROM useracc_data WHERE user_id = 'admin'")
        admin_exists = cursor.fetchone()[0]

        if admin_exists == 0:
            admin_insert = """
            INSERT INTO useracc_data (first_name, last_name, email, user_id, password, status)
            VALUES ('Admin', 'User', '<EMAIL>', 'admin', '1234', 'active')
            """
            cursor.execute(admin_insert)
            # Default admin account created

        # Check if original_image column exists, add if missing
        try:
            cursor.execute("SHOW COLUMNS FROM images_result LIKE 'original_image'")
            if not cursor.fetchone():
                # Adding missing 'original_image' column
                cursor.execute("ALTER TABLE images_result ADD COLUMN original_image VARCHAR(255) DEFAULT NULL")
                # Added 'original_image' column successfully
        except Exception as e:
            pass

        # Check if processing_time column exists, add if missing
        try:
            cursor.execute("SHOW COLUMNS FROM images_result LIKE 'processing_time'")
            if not cursor.fetchone():
                # Adding missing 'processing_time' column
                cursor.execute("ALTER TABLE images_result ADD COLUMN processing_time DECIMAL(6,3) DEFAULT NULL")
                # Added 'processing_time' column successfully
        except Exception as e:
            pass

        # 🔧 MIGRATE OLD COLUMN NAMES TO NEW SCAN_DATE COLUMNS
        # Migrating database columns to use 'scan_date'

        # Migrate patients table: updated_at -> scan_date
        try:
            cursor.execute("SHOW COLUMNS FROM patients LIKE 'updated_at'")
            if cursor.fetchone():
                # Renaming 'updated_at' to 'scan_date' in patients table
                cursor.execute("ALTER TABLE patients CHANGE COLUMN updated_at scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
                # Renamed patients.updated_at to scan_date successfully
        except Exception as e:
            pass

        # Migrate uploads table: upload_date -> scan_date
        try:
            cursor.execute("SHOW COLUMNS FROM uploads LIKE 'upload_date'")
            if cursor.fetchone():
                # Renaming 'upload_date' to 'scan_date' in uploads table
                cursor.execute("ALTER TABLE uploads CHANGE COLUMN upload_date scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP")
                # Renamed uploads.upload_date to scan_date successfully
        except Exception as e:
            print(f"⚠️ Could not migrate uploads.upload_date: {e}")

        # Migrate images_result table: analysis_date -> scan_date
        try:
            cursor.execute("SHOW COLUMNS FROM images_result LIKE 'analysis_date'")
            if cursor.fetchone():
                print("🔧 Renaming 'analysis_date' to 'scan_date' in images_result table...")
                cursor.execute("ALTER TABLE images_result CHANGE COLUMN analysis_date scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP")
                # Also update the index name
                cursor.execute("DROP INDEX idx_analysis_date ON images_result")
                cursor.execute("CREATE INDEX idx_scan_date ON images_result (scan_date)")
                print("✅ Renamed images_result.analysis_date to scan_date successfully")
        except Exception as e:
            print(f"⚠️ Could not migrate images_result.analysis_date: {e}")

        print("✅ Database column migration completed")

        # Check if primary key is correct (should be 'id', not 'patient_id')
        try:
            cursor.execute("SHOW KEYS FROM images_result WHERE Key_name = 'PRIMARY'")
            primary_key_info = cursor.fetchone()
            if primary_key_info and len(primary_key_info) > 0:
                # Get column name from the result
                if isinstance(primary_key_info, dict):
                    column_name = primary_key_info.get('Column_name', '')
                elif isinstance(primary_key_info, (list, tuple)):
                    column_name = primary_key_info[4] if len(primary_key_info) > 4 else ''
                else:
                    column_name = str(primary_key_info)

                if 'patient_id' in column_name:
                    print("🔧 Fixing primary key in images_result table (changing from patient_id to id)...")
                    cursor.execute("ALTER TABLE images_result DROP PRIMARY KEY")
                    cursor.execute("ALTER TABLE images_result ADD PRIMARY KEY (id)")
                    print("✅ Fixed primary key successfully")
                else:
                    print(f"✅ Primary key is correct: {column_name}")
        except Exception as e:
            print(f"⚠️ Could not check/fix primary key: {e}")
            # If there's an issue, let's recreate the table with correct structure
            try:
                print("🔧 Recreating images_result table with correct structure...")
                cursor.execute("DROP TABLE IF EXISTS images_result")
                cursor.execute(results_table)
                print("✅ Recreated images_result table successfully")
            except Exception as e2:
                print(f"❌ Could not recreate table: {e2}")

        connection.commit()
        return True

    except mysql.connector.Error as e:
        print(f"Error creating tables: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

# Initialize database tables on startup
create_tables_if_not_exist()

# Load model
model = YOLO("model/rae_2130best.pt")

UPLOAD_FOLDER = 'static/uploads'
RESULT_FOLDER = 'static/results'
PATIENT_UPLOAD_FOLDER = 'static/uploads'  # For patient-specific uploads (fixed to use static/uploads)

# External patient directory configuration
# Try multiple possible paths for the patient directory
possible_patient_dirs = [
    r'C:\Users\<USER>\Downloads\University\Patient',  # Direct path
    os.path.expanduser('~/Downloads/University/Patient'),  # Expanduser path
    os.path.join(os.path.expanduser('~'), 'Downloads', 'University', 'Patient'),  # Constructed path
]

EXTERNAL_PATIENT_DIR = None
for patient_dir in possible_patient_dirs:
    if os.path.exists(patient_dir):
        EXTERNAL_PATIENT_DIR = patient_dir
        print(f"✅ Found external patient directory: {EXTERNAL_PATIENT_DIR}")
        break

if not EXTERNAL_PATIENT_DIR:
    # Fallback to using static/uploads as external directory
    EXTERNAL_PATIENT_DIR = PATIENT_UPLOAD_FOLDER
    print(f"⚠️ External patient directory not found, using fallback: {EXTERNAL_PATIENT_DIR}")
else:
    print(f"🗂️ Using external patient directory: {EXTERNAL_PATIENT_DIR}")

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULT_FOLDER, exist_ok=True)
os.makedirs(PATIENT_UPLOAD_FOLDER, exist_ok=True)



# Utility Functions
def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def create_enhanced_mask_visualization(image, mask, predicted_class, confidence, bbox_coords):
    """
    Create enhanced visualization with segmentation mask and bounding box

    Args:
        image: Original image (numpy array)
        mask: Segmentation mask (numpy array)
        predicted_class: Predicted tumor class
        confidence: Model confidence score
        bbox_coords: Tuple of (x1, y1, x2, y2) bounding box coordinates

    Returns:
        Enhanced image with mask overlay and bounding box
    """
    import numpy as np

    # Create a copy of the original image
    result_image = image.copy()

    # Create colored mask overlay with gradient effect
    colored_mask = np.zeros_like(image)

    # Use different colors for different tumor types
    if 'glioma' in predicted_class.lower():
        mask_color = [0, 100, 255]  # Orange-red for glioma
    elif 'meningioma' in predicted_class.lower():
        mask_color = [0, 255, 255]  # Yellow for meningioma
    elif 'pituitary' in predicted_class.lower():
        mask_color = [255, 0, 255]  # Magenta for pituitary
    else:
        mask_color = [0, 255, 255]  # Default yellow

    # Apply mask color
    colored_mask[mask > 0] = mask_color

    # Create mask with smooth edges using morphological operations
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    smooth_mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    smooth_mask = cv2.GaussianBlur(smooth_mask, (3, 3), 0)

    # Apply colored mask with transparency
    alpha = 0.4  # Transparency level
    result_image = cv2.addWeighted(result_image, 1-alpha, colored_mask, alpha, 0)

    # Add mask contour for better visibility
    contours, _ = cv2.findContours(smooth_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    if contours:
        # Draw contour with thicker line
        cv2.drawContours(result_image, contours, -1, (255, 255, 255), 2)  # White contour
        cv2.drawContours(result_image, contours, -1, mask_color, 1)  # Colored inner line

    # Draw bounding box
    x1, y1, x2, y2 = bbox_coords
    cv2.rectangle(result_image, (x1, y1), (x2, y2), (0, 255, 0), 2)

    # Add enhanced label with background
    label = f"{predicted_class.replace('_', ' ').title()}: {confidence:.2f}"

    # Calculate text size for background
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    thickness = 2
    (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)

    # Draw label background
    label_bg_start = (x1, y1 - text_height - 10)
    label_bg_end = (x1 + text_width + 10, y1)
    cv2.rectangle(result_image, label_bg_start, label_bg_end, (0, 0, 0), -1)  # Black background
    cv2.rectangle(result_image, label_bg_start, label_bg_end, (0, 255, 0), 1)  # Green border

    # Draw label text
    cv2.putText(result_image, label, (x1 + 5, y1 - 5), font, font_scale, (255, 255, 255), thickness)

    return result_image

def sanitize_filename(filename):
    """Sanitize filename for safe storage"""
    filename = secure_filename(filename)
    # Remove special characters except dots, hyphens, and underscores
    import re
    filename = re.sub(r'[^a-zA-Z0-9._-]', '_', filename)
    # Limit filename length
    if len(filename) > 100:
        name, ext = os.path.splitext(filename)
        filename = name[:95] + ext
    return filename

def generate_unique_filename(original_filename):
    """Generate unique filename to prevent conflicts"""
    name, ext = os.path.splitext(sanitize_filename(original_filename))
    unique_id = str(uuid.uuid4())[:8]
    timestamp = str(int(time.time()))
    return f"{name}_{unique_id}_{timestamp}{ext}"

def save_patient_info(patient_data):
    """Save patient information to 'patients' table"""
    connection = get_db_connection()
    if not connection:
        return False, "Database connection failed"

    try:
        cursor = connection.cursor()

        # Check if patient already exists
        check_query = "SELECT id FROM patients WHERE patient_id = %s"
        cursor.execute(check_query, (patient_data['patient_id'],))
        existing_patient = cursor.fetchone()

        if existing_patient:
            # Update existing patient in 'patients' table
            print(f"🔄 Updating existing patient in 'patients' table: {patient_data['patient_id']}")
            update_query = """
            UPDATE patients SET
                patient_name = %s, gender = %s, age = %s,
                date_of_birth = %s, radiologist = %s, scan_date = NOW()
            WHERE patient_id = %s
            """
            cursor.execute(update_query, (
                patient_data['patient_name'],
                patient_data['gender'],
                patient_data['age'],
                patient_data['date_of_birth'],
                patient_data['radiologist'],
                patient_data['patient_id']
            ))
        else:
            # Insert new patient into 'patients' table
            print(f"➕ Inserting new patient into 'patients' table: {patient_data['patient_id']}")
            insert_query = """
            INSERT INTO patients (patient_id, patient_name, gender, age, date_of_birth, radiologist)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (
                patient_data['patient_id'],
                patient_data['patient_name'],
                patient_data['gender'],
                patient_data['age'],
                patient_data['date_of_birth'],
                patient_data['radiologist']
            ))

        connection.commit()
        print(f"✅ Patient info saved to 'patients' table successfully: {patient_data['patient_id']}")
        return True, "Patient information saved to 'patients' table successfully"

    except mysql.connector.Error as e:
        print(f"❌ Error saving patient to 'patients' table: {e}")
        return False, f"Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def save_file_info(patient_id, file_info):
    """Save uploaded file information to uploads table"""
    connection = get_db_connection()
    if not connection:
        return False

    try:
        cursor = connection.cursor()
        # Insert into uploads table (matching Upload.sql structure exactly)
        insert_query = """
        INSERT INTO uploads (patient_id, file_name, file_path, file_size, file_type)
        VALUES (%s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            patient_id,
            file_info['file_name'],
            file_info['file_path'],
            file_info['file_size'],
            file_info['file_type']
        ))
        connection.commit()
        return True

    except mysql.connector.Error as e:
        print(f"❌ Error saving file to uploads table: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def process_image_with_yolo(image_path, original_filename, patient_id, patient_name):
    """Process uploaded image with YOLO model and save results to images_result table"""
    try:
        import time
        import numpy as np
        start_time = time.time()

        print(f"🧠 Starting YOLO analysis for: {original_filename}")
        print(f"📁 Image path: {image_path}")
        print(f"📂 File exists: {os.path.exists(image_path)}")

        # Check if image file exists
        if not os.path.exists(image_path):
            print(f"❌ Image file not found: {image_path}")
            return None

        # Run YOLO model
        print(f"🤖 Running YOLO model on image...")
        results = model(image_path)[0]
        image = cv2.imread(image_path)

        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return None

        print(f"✅ Image loaded successfully, shape: {image.shape}")

        # Initialize default values
        predicted_class = "No tumor"
        confidence = 0.0
        x1 = y1 = x2 = y2 = width = height = mask_area = 0

        def refine_predicted_class(raw_class):
            """Map raw predicted class to standardized class name"""
            s = str(raw_class).lower()
            if 'pituitary' in s:
                return 'pituitary_tumor'
            elif 'glioma' in s:
                return 'glioma_tumor'
            elif 'meningioma' in s:
                return 'meningioma_tumor'
            elif any(word in s for word in ['notumor', 'notumour', 'no tumor', 'no tumour', 'no_tumor', 'no_tumour','No Tumor','No Tumour','No tumor', 'No tumour', 'NoTumor', 'NoTumour']):
                return 'no_tumour'
            else:
                return raw_class

        # Process detection results
        if results.boxes:
            top = results.boxes[0]
            x1, y1, x2, y2 = map(int, top.xyxy[0])
            width = x2 - x1
            height = y2 - y1
            confidence = float(top.conf[0])
            raw_predicted_class = model.names[int(top.cls[0])]
            predicted_class = refine_predicted_class(raw_predicted_class)

            # Process segmentation masks if available
            if hasattr(results, 'masks') and results.masks is not None:
                print("🎯 Segmentation masks detected - processing...")

                # Get the mask for the first detection
                mask = results.masks.data[0].cpu().numpy()  # Get mask as numpy array
                mask = (mask * 255).astype(np.uint8)  # Convert to 8-bit

                # Resize mask to match image dimensions if needed
                if mask.shape != image.shape[:2]:
                    mask = cv2.resize(mask, (image.shape[1], image.shape[0]))

                # Calculate actual mask area (number of pixels in the mask)
                mask_area = np.sum(mask > 0)
                print(f"📏 Actual mask area: {mask_area} pixels")

                # Use enhanced visualization
                image = create_enhanced_mask_visualization(
                    image, mask, predicted_class, confidence, (x1, y1, x2, y2)
                )

                print("✅ Enhanced segmentation mask applied successfully")
            else:
                # Fallback to bounding box area if no masks available
                mask_area = width * height
                print("⚠️ No segmentation masks available, using bounding box area")

                # Draw simple bounding box for non-segmentation models
                label = f"{predicted_class} {confidence:.2f}"
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(image, label, (x1, y1 - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        # Save result image with bounding boxes - GUARANTEED SAVE
        result_filename = f"result_{original_filename}"

        # Create patient-specific result folder in static/results
        patient_result_folder = os.path.join(RESULT_FOLDER, f"result_{patient_id}")
        os.makedirs(patient_result_folder, exist_ok=True)

        print(f"📁 Patient result folder: {patient_result_folder}")
        print(f"📁 Folder exists: {os.path.exists(patient_result_folder)}")

        result_path = os.path.join(patient_result_folder, result_filename)
        print(f"💾 Saving processed image to: {result_path}")

        # GUARANTEED SAVE with verification
        save_success = cv2.imwrite(result_path, image)
        if save_success and os.path.exists(result_path):
            file_size = os.path.getsize(result_path)
            print(f"✅ Processed image saved successfully: {result_path} ({file_size} bytes)")
        else:
            print(f"❌ FAILED to save processed image: {result_path}")
            # Try alternative save location
            fallback_path = os.path.join(RESULT_FOLDER, result_filename)
            print(f"🔄 Trying fallback location: {fallback_path}")
            save_success = cv2.imwrite(fallback_path, image)
            if save_success and os.path.exists(fallback_path):
                result_path = fallback_path
                print(f"✅ Processed image saved to fallback location: {result_path}")
            else:
                print(f"❌ CRITICAL: Could not save processed image anywhere!")
                return None

        # Also save to external patient directory if it exists
        try:
            external_patient_folder = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
            if os.path.exists(external_patient_folder):
                external_result_path = os.path.join(external_patient_folder, result_filename)
                cv2.imwrite(external_result_path, image)
                print(f"✅ Result image also saved to external directory: {external_result_path}")
            else:
                print(f"⚠️ External patient folder not found: {external_patient_folder}")
        except Exception as e:
            print(f"⚠️ Could not save to external directory: {e}")

        # (Google Drive upload removed)

        # Calculate processing time
        processing_time = round(time.time() - start_time, 3)
        print(f"⏱️ Analysis completed in {processing_time} seconds")

        # VERIFY the processed image exists before saving to database
        if not os.path.exists(result_path):
            print(f"❌ CRITICAL ERROR: Processed image not found at {result_path}")
            return None

        # Get the actual filename from the saved path (in case fallback was used)
        actual_result_filename = os.path.basename(result_path)
        actual_result_folder = os.path.dirname(result_path)

        print(f"✅ Verified processed image exists: {result_path}")
        print(f"📄 Actual filename: {actual_result_filename}")
        print(f"📁 Actual folder: {actual_result_folder}")

        # Prepare result data for images_result table
        # Convert numpy types to Python native types to avoid database conversion errors
        result_data = {
            'result_image': actual_result_filename,  # Use actual filename
            'predicted_class': str(predicted_class),
            'confidence': float(confidence),
            'x1': int(x1), 'y1': int(y1), 'x2': int(x2), 'y2': int(y2),
            'width': int(width), 'height': int(height),
            'mask_area': int(mask_area),
            'processing_time': float(processing_time),
            'original_filename': original_filename,
            'actual_result_path': result_path  # Store actual path for debugging
        }

        # Save analysis results to images_result table
        if save_analysis_result(patient_id, patient_name, result_data, original_filename, processing_time):
            print(f"✅ Analysis result saved to 'images_result' table: {predicted_class} (confidence: {confidence:.4f})")

            # FINAL VERIFICATION: Double-check that everything is properly saved
            final_verification_path = os.path.join('static', 'results', f'result_{patient_id}', actual_result_filename)
            if os.path.exists(final_verification_path):
                print(f"✅ FINAL VERIFICATION PASSED: Processed image accessible at {final_verification_path}")
            else:
                print(f"⚠️ FINAL VERIFICATION WARNING: Image may not be accessible at expected path {final_verification_path}")
                # Check if it's in the fallback location
                fallback_verification_path = os.path.join('static', 'results', actual_result_filename)
                if os.path.exists(fallback_verification_path):
                    print(f"✅ Image found in fallback location: {fallback_verification_path}")
                else:
                    print(f"❌ CRITICAL: Image not found in any expected location!")

            return result_data
        else:
            print(f"❌ Failed to save analysis result to 'images_result' table")
            return None

    except Exception as e:
        print(f"❌ Error in YOLO processing: {e}")
        return None

def verify_processed_image_exists(patient_id, result_image):
    """Verify that the processed image actually exists and is accessible"""
    possible_paths = [
        os.path.join('static', 'results', f'result_{patient_id}', result_image),
        os.path.join('static', 'results', result_image),
        os.path.join('static', 'results', 'result_UNKNOWN', result_image),
    ]

    for path in possible_paths:
        if os.path.exists(path):
            file_size = os.path.getsize(path)
            print(f"✅ Verified processed image exists: {path} ({file_size} bytes)")
            return True, path

    print(f"❌ Processed image verification FAILED: {result_image} not found in any location")
    return False, None

def save_analysis_result(patient_id, patient_name, result_data, original_image=None, processing_time=None):
    """Save YOLO model analysis results to 'images_result' table"""

    # MANDATORY VERIFICATION: Ensure processed image exists before saving to database
    result_image = result_data.get('result_image')
    if result_image:
        image_exists, verified_path = verify_processed_image_exists(patient_id, result_image)
        if not image_exists:
            return False

    connection = get_db_connection()
    if not connection:
        print("❌ Database connection failed for saving analysis result")
        return False

    try:
        cursor = connection.cursor()
        print(f"💾 Saving analysis result to 'images_result' table for patient: {patient_id}")

        # Use old schema for backward compatibility
        insert_query = """
        INSERT INTO images_result
        (patient_id, patient_name, result_image, predicted_class, confidence,
         x1, y1, x2, y2, width, height, mask_area, original_image, processing_time)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        cursor.execute(insert_query, (
            patient_id,
            patient_name,
            result_data['result_image'],
            result_data['predicted_class'],
            result_data['confidence'],
            result_data['x1'],
            result_data['y1'],
            result_data['x2'],
            result_data['y2'],
            result_data['width'],
            result_data['height'],
            result_data['mask_area'],
            original_image,
            processing_time
        ))

        connection.commit()
        print(f"✅ Analysis result saved to 'images_result' table: {result_data['predicted_class']} (confidence: {result_data['confidence']})")


        # VERIFY THE SAVE WORKED
        cursor.execute("SELECT COUNT(*) FROM images_result WHERE patient_id = %s", (patient_id,))
        count = cursor.fetchone()[0]
        print(f"🔍 DEBUG: Total records for patient {patient_id}: {count}")

        return True

    except mysql.connector.Error as e:
        print(f"❌ Error saving analysis result to 'images_result' table: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

# Home page route (pre-login)
@app.route('/')
def home():
    return render_template('home.html')

# About page route (pre-login)
@app.route('/about')
def about():
    return render_template('about.html')

# Contact form handler
@app.route('/contact', methods=['POST'])
def contact():
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        message = request.form.get('message')

        # Here you can add logic to save the contact form data to database
        # For now, we'll just flash a success message
        flash(f'✅ Thank you {name}! Your message has been sent successfully.', 'success')
        return redirect(url_for('about'))

# Registration page route
@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        first_name = request.form.get('first_name')
        last_name = request.form.get('last_name')
        email = request.form.get('email')
        user_id = request.form.get('user_id')
        password = request.form.get('password')

        # Validate required fields
        if not all([first_name, last_name, email, user_id, password]):
            flash('❌ All fields are required.', 'error')
            return render_template('register.html')

        try:
            connection = mysql.connector.connect(**DB_CONFIG)
            cursor = connection.cursor()

            # Check if user_id or email already exists
            cursor.execute("SELECT user_id, email FROM useracc_data WHERE user_id = %s OR email = %s", (user_id, email))
            existing_user = cursor.fetchone()

            if existing_user:
                if existing_user[0] == user_id:
                    flash('❌ User ID already exists. Please choose a different one.', 'error')
                else:
                    flash('❌ Email already registered. Please use a different email.', 'error')
                return render_template('register.html')

            # Insert new user
            insert_query = """
                INSERT INTO useracc_data (first_name, last_name, email, user_id, password)
                VALUES (%s, %s, %s, %s, %s)
            """
            cursor.execute(insert_query, (first_name, last_name, email, user_id, password))
            connection.commit()

            flash('✅ Account created successfully! You can now log in.', 'success')
            return redirect(url_for('login'))

        except mysql.connector.Error as e:
            flash(f'❌ Database error: {str(e)}', 'error')
            return render_template('register.html')
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    return render_template('register.html')

# Login page route
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        connection = None
        try:
            connection = mysql.connector.connect(**DB_CONFIG)
            cursor = connection.cursor()

            # Check user credentials in database
            cursor.execute("SELECT user_id, password, first_name, last_name FROM useracc_data WHERE user_id = %s AND password = %s AND status = 'active'", (username, password))
            user = cursor.fetchone()

            if user:
                session['logged_in'] = True
                session['username'] = user[0]
                session['first_name'] = user[2]
                session['last_name'] = user[3]
                return redirect(url_for('upload'))
            else:
                flash('❌ Invalid username or password. Please try again.', 'error')
                return render_template('Login.html')

        except mysql.connector.Error as e:
            flash(f'❌ Database error: {str(e)}', 'error')
            return render_template('Login.html')
        finally:
            if connection and connection.is_connected():
                cursor.close()
                connection.close()

    return render_template('Login.html')

@app.route('/select')
def select():
    return render_template('Select.html')

@app.route('/get_next_patient_id')
def get_next_patient_id():
    """Get the next sequential patient ID based on the last patient in the database"""
    connection = get_db_connection()
    if not connection:
        return jsonify({'success': False, 'message': 'Database connection failed'})

    try:
        cursor = connection.cursor(dictionary=True)

        # Get the last patient ID from the database, ordered by patient_id
        cursor.execute("""
            SELECT patient_id
            FROM patients
            WHERE patient_id REGEXP '^P[0-9]+$'
            ORDER BY CAST(SUBSTRING(patient_id, 2) AS UNSIGNED) DESC
            LIMIT 1
        """)

        result = cursor.fetchone()

        if result:
            # Extract the number from the last patient ID (e.g., P0009 -> 9)
            last_id = result['patient_id']
            number_part = int(last_id[1:])  # Remove 'P' and convert to int
            next_number = number_part + 1
            next_patient_id = f"P{next_number:04d}"  # Format as P0010, P0011, etc.
        else:
            # No patients in database, start with P0001
            next_patient_id = "P0001"

        return jsonify({'success': True, 'next_patient_id': next_patient_id})

    except mysql.connector.Error as e:
        print(f"Database error in get_next_patient_id: {e}")
        return jsonify({'success': False, 'message': f'Database error: {str(e)}'})
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/upload', methods=['GET', 'POST'])
def upload():
    if request.method == 'POST':
        try:
            print(f"🔄 POST request received at /upload")
            print(f"📋 Form data keys: {list(request.form.keys())}")
            print(f"📁 Files: {list(request.files.keys())}")

            # Handle patient information and file uploads
            if 'patient_id' in request.form:
                print(f"👤 Patient upload detected - patient_id: {request.form.get('patient_id')}")
                return handle_patient_upload()
            else:
                print(f"📄 Single file upload detected")
                # Handle single file upload (original functionality)
                return handle_single_file_upload()
        except Exception as e:
            print(f"❌ Upload error: {e}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'message': f'Upload error: {str(e)}'})

    return render_template('upload_new.html')

# Route to serve logo image
@app.route('/RadioLens_Logo.png')
def logo_image():
    return send_from_directory('Images', 'RadioLens_Logo.png', mimetype='image/png')

# Route to serve images from Images folder
@app.route('/Images/<filename>')
def serve_images(filename):
    return send_from_directory('Images', filename)



def handle_patient_upload():
    """Handle patient information and multiple file uploads"""
    print(f"🏥 Starting handle_patient_upload()")

    # Extract patient information
    patient_data = {
        'patient_id': request.form.get('patient_id', '').strip(),
        'patient_name': request.form.get('patient_name', '').strip(),
        'gender': request.form.get('gender', '').strip(),
        'age': request.form.get('age', '').strip(),
        'date_of_birth': request.form.get('date_of_birth', '').strip(),
        'radiologist': request.form.get('radiologist', '').strip()
    }

    print(f"👤 Patient data: {patient_data}")
    print(f"📁 Files received: {len(request.files.getlist('files[]'))} files")

    # Validate required fields
    required_fields = ['patient_id', 'patient_name', 'gender', 'age', 'date_of_birth', 'radiologist']
    missing_fields = [field for field in required_fields if not patient_data[field]]

    if missing_fields:
        return jsonify({
            'success': False,
            'message': f'Missing required fields: {", ".join(missing_fields)}'
        })

    # Validate age
    try:
        age = int(patient_data['age'])
        if age < 0 or age > 150:
            raise ValueError("Invalid age range")
        patient_data['age'] = age
    except ValueError:
        return jsonify({'success': False, 'message': 'Please enter a valid age (0-150)'})

    # Convert date format if needed (DD/MM/YYYY to YYYY-MM-DD)
    date_of_birth = patient_data['date_of_birth']
    if '/' in date_of_birth:
        try:
            # Assume DD/MM/YYYY format
            day, month, year = date_of_birth.split('/')
            patient_data['date_of_birth'] = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        except ValueError:
            return jsonify({'success': False, 'message': 'Invalid date format. Use YYYY-MM-DD or DD/MM/YYYY'})

    # Save patient information to 'patients' table
    print(f"💾 Saving patient info to 'patients' table: {patient_data['patient_id']}")
    success, message = save_patient_info(patient_data)
    if not success:
        return jsonify({'success': False, 'message': f"Failed to save patient info: {message}"})

    # Handle file uploads - save to disk and 'uploads' table
    uploaded_files = []
    failed_files = []

    if 'files[]' in request.files:
        files = request.files.getlist('files[]')
        patient_folder = os.path.join(PATIENT_UPLOAD_FOLDER, patient_data['patient_id'])
        os.makedirs(patient_folder, exist_ok=True)

        print(f"📁 Processing {len(files)} files for patient {patient_data['patient_id']}")
        print(f"📂 Patient folder: {patient_folder}")

        for file in files:
            if file and file.filename:
                if allowed_file(file.filename):
                    try:
                        # Generate unique filename
                        unique_filename = generate_unique_filename(file.filename)
                        file_path = os.path.join(patient_folder, unique_filename)

                        # Save file
                        file.save(file_path)

                        # Get file info
                        file_size = os.path.getsize(file_path)
                        file_type = file.content_type or 'application/octet-stream'

                        # Prepare file info for uploads table
                        file_info = {
                            'file_name': file.filename,
                            'file_path': file_path,
                            'file_size': file_size,
                            'file_type': file_type
                        }

                        # Save file info to 'uploads' table
                        print(f"💾 Saving file to 'uploads' table: {file.filename}")
                        if save_file_info(patient_data['patient_id'], file_info):
                            uploaded_files.append({
                                'filename': file.filename,
                                'unique_filename': unique_filename,
                                'file_path': file_path
                            })
                            print(f"✅ File saved successfully: {file.filename}")
                        else:
                            failed_files.append(f"{file.filename} (database error)")
                            print(f"❌ Failed to save file to database: {file.filename}")

                    except Exception as e:
                        failed_files.append(f"{file.filename} ({str(e)})")
                else:
                    failed_files.append(f"{file.filename} (invalid file type)")

    # 🧠 TRIGGER YOLO ANALYSIS for uploaded images
    analysis_results = []
    analysis_failed = []

    if uploaded_files:
        print(f"🧠 Starting YOLO analysis for {len(uploaded_files)} uploaded images...")

        for file_info in uploaded_files:
            try:
                print(f"🔍 Analyzing: {file_info['filename']}")
                print(f"📁 File path: {file_info['file_path']}")
                print(f"📂 File exists: {os.path.exists(file_info['file_path'])}")

                # Check if it's an image file
                if not is_image_file(file_info['filename']):
                    print(f"⚠️ Skipping non-image file: {file_info['filename']}")
                    continue

                print(f"🧠 Processing image with YOLO...")
                print(f"🔍 About to call process_image_with_yolo with:")
                print(f"   - Image path: {file_info['file_path']}")
                print(f"   - Filename: {file_info['filename']}")
                print(f"   - Patient ID: {patient_data['patient_id']}")
                print(f"   - Patient Name: {patient_data['patient_name']}")

                analysis_result = process_image_with_yolo(
                    file_info['file_path'],
                    file_info['filename'],
                    patient_data['patient_id'],
                    patient_data['patient_name']
                )

                print(f"🎯 YOLO processing result: {analysis_result is not None}")

                # GUARANTEE: Verify the processed image was created successfully
                if analysis_result:
                    result_image = analysis_result.get('result_image')
                    if result_image:
                        image_exists, verified_path = verify_processed_image_exists(
                            patient_data['patient_id'], result_image
                        )
                        if image_exists:
                            print(f"✅ GUARANTEED: Processed image verified for {file_info['filename']}")
                        else:
                            print(f"❌ CRITICAL: Processed image missing for {file_info['filename']} - attempting recovery...")
                            # Attempt to regenerate the processed image
                            recovery_result = process_image_with_yolo(
                                file_info['file_path'],
                                file_info['filename'],
                                patient_data['patient_id'],
                                patient_data['patient_name']
                            )
                            if recovery_result:
                                print(f"✅ Recovery successful for {file_info['filename']}")
                                analysis_result = recovery_result
                            else:
                                print(f"❌ Recovery failed for {file_info['filename']}")
                                analysis_result = None

                if analysis_result:
                    analysis_results.append(analysis_result)
                    print(f"✅ Analysis completed: {file_info['filename']} - {analysis_result['predicted_class']}")
                else:
                    analysis_failed.append(file_info['filename'])
                    print(f"❌ Analysis failed: {file_info['filename']}")

            except Exception as e:
                analysis_failed.append(f"{file_info['filename']} ({str(e)})")
                print(f"❌ Analysis error for {file_info['filename']}: {e}")

    # Prepare enhanced response with YOLO analysis results
    response = {
        'success': True,
        'message': f'{len(uploaded_files)} file(s) uploaded and analyzed successfully!',
        'patient_id': patient_data['patient_id'],
        'patient_name': patient_data['patient_name'],
        'files_uploaded': len(uploaded_files),
        'files_failed': len(failed_files),
        'analysis_completed': len(analysis_results),
        'analysis_failed': len(analysis_failed),
        'analysis_results': [
            {
                'original_image': result['original_filename'],
                'predicted_class': result['predicted_class'],
                'confidence': result['confidence'],
                'processing_time': result['processing_time'],
                'mask_area': result['mask_area'],
                'width': result['width'],
                'height': result['height'],
                'result_image': result['result_image']
            } for result in analysis_results
        ],
        'database_tables_used': {
            'patients': 'Patient information stored',
            'uploads': f'{len(uploaded_files)} files stored',
            'images_result': f'{len(analysis_results)} analysis results stored'
        }
    }

    # Add warnings for failed uploads and analysis
    warnings = []
    if failed_files:
        warnings.extend([f"Upload failed: {f}" for f in failed_files])
    if analysis_failed:
        warnings.extend([f"Analysis failed: {f}" for f in analysis_failed])

    if warnings:
        response['warnings'] = warnings

    print(f"✅ Sending response: {len(analysis_results)} analysis results")
    print(f"📊 Response summary: {response['database_tables_used']}")

    return jsonify(response)

def handle_single_file_upload():
    """Handle single file upload (original functionality)"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': 'No file selected'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No file selected'})

    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        return redirect(url_for('report', filename=filename))
    else:
        return jsonify({'success': False, 'message': 'Invalid file type'})

@app.route('/report')
def report():
    # Get parameters from URL
    filename = request.args.get('filename')
    patient_id = request.args.get('patient_id')
    result_id = request.args.get('result_id')  # For viewing existing results

    # Check if we have a filename parameter (for new analysis)
    if filename:
        # Validate filename is not None or empty
        if not filename or filename.strip() == '':
            flash('❌ No filename provided for analysis', 'error')
            return redirect(url_for('upload'))

        # Handle new file analysis
        return handle_new_analysis(filename, patient_id)

    # Check if we have a result_id (for viewing existing results)
    elif result_id:
        return view_existing_result(result_id)

    # Check if we have a patient_id (show latest result for patient)
    elif patient_id:
        return view_patient_latest_result(patient_id)

    # No parameters provided - show report dashboard
    return show_report_dashboard()

def handle_new_analysis(filename, patient_id=None):
    """Handle new file analysis"""
    try:
        # Validate file exists
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(file_path):
            flash(f'❌ File not found: {filename}', 'error')
            return redirect(url_for('upload'))

        # Handle patient ID assignment
        patient_info = None
        if patient_id:
            # Patient ID was provided, validate it
            patient_info = get_patient_info(patient_id)
            if not patient_info:
                flash(f'❌ Patient not found: {patient_id}', 'error')
                patient_id = 'UNKNOWN'
        else:
            # No patient ID provided, try to extract from filename
            print(f"🔍 No patient_id provided, attempting to extract from filename: {filename}")

            # Use the utility function to extract patient ID
            extracted_patient_id = extract_patient_id_from_filename(filename)

            if extracted_patient_id:
                print(f"✅ Extracted patient ID from filename: {extracted_patient_id}")
                patient_id = extracted_patient_id
                # Try to get patient info for extracted ID
                patient_info = get_patient_info(patient_id)
                if not patient_info:
                    print(f"⚠️ Extracted patient ID {patient_id} not found in database")
            else:
                print(f"⚠️ Could not extract patient ID from filename: {filename}")
                patient_id = 'UNKNOWN'

        # Start timing for processing
        import time
        import numpy as np
        start_time = time.time()

        print(f"🧠 Starting YOLO analysis for: {filename}")
        results = model(file_path)[0]
        image = cv2.imread(file_path)

        predicted_class = "No tumor"
        confidence = 0.0
        x1 = y1 = x2 = y2 = width = height = mask_area = 0

        if results.boxes:
            top = results.boxes[0]
            x1, y1, x2, y2 = map(int, top.xyxy[0])
            width = x2 - x1
            height = y2 - y1
            confidence = float(top.conf[0])
            predicted_class = model.names[int(top.cls[0])]

            # Process segmentation masks if available
            if hasattr(results, 'masks') and results.masks is not None:
                print("🎯 Segmentation masks detected - processing...")

                # Get the mask for the first detection
                mask = results.masks.data[0].cpu().numpy()  # Get mask as numpy array
                mask = (mask * 255).astype(np.uint8)  # Convert to 8-bit

                # Resize mask to match image dimensions if needed
                if mask.shape != image.shape[:2]:
                    mask = cv2.resize(mask, (image.shape[1], image.shape[0]))

                # Calculate actual mask area (number of pixels in the mask)
                mask_area = np.sum(mask > 0)
                print(f"📏 Actual mask area: {mask_area} pixels")

                # Use enhanced visualization
                image = create_enhanced_mask_visualization(
                    image, mask, predicted_class, confidence, (x1, y1, x2, y2)
                )

                print("✅ Enhanced segmentation mask applied successfully")
            else:
                # Fallback to bounding box area if no masks available
                mask_area = width * height
                print("⚠️ No segmentation masks available, using bounding box area")

                # Draw simple bounding box for non-segmentation models
                label = f"{predicted_class} {confidence:.2f}"
                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(image, label, (x1, y1 - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255,255,255), 1)

        result_image = f"result_{filename}"

        # Create patient-specific result folder in static/results
        patient_result_folder = os.path.join(RESULT_FOLDER, f"result_{patient_id}")
        os.makedirs(patient_result_folder, exist_ok=True)

        result_path = os.path.join(patient_result_folder, result_image)
        cv2.imwrite(result_path, image)

        # Also save to external patient directory if it exists
        try:
            external_patient_folder = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
            if os.path.exists(external_patient_folder):
                external_result_path = os.path.join(external_patient_folder, result_image)
                cv2.imwrite(external_result_path, image)
                print(f"✅ Result image also saved to external directory: {external_result_path}")
            else:
                print(f"⚠️ External patient folder not found: {external_patient_folder}")
        except Exception as e:
            print(f"⚠️ Could not save to external directory: {e}")

        # Calculate processing time
        processing_time = round(time.time() - start_time, 3)
        print(f"⏱️ Analysis completed in {processing_time} seconds")

        # Prepare analysis results for database
        # Convert numpy types to Python native types to avoid database conversion errors
        result_data = {
            'result_image': result_image,
            'predicted_class': str(predicted_class),
            'confidence': float(confidence),
            'x1': int(x1), 'y1': int(y1), 'x2': int(x2), 'y2': int(y2),
            'width': int(width), 'height': int(height),
            'mask_area': int(mask_area),

            'processing_time': float(processing_time)
        }

        # Save analysis results to database
        if patient_id and patient_id != 'UNKNOWN':
            patient_name = patient_info['patient_name'] if patient_info else 'Unknown Patient'
            print(f"💾 Saving analysis result to database for patient: {patient_id}")
            save_analysis_result(patient_id, patient_name, result_data, filename, processing_time)
        else:
            print("⚠️ No patient ID provided, analysis result not saved to database")
            patient_info = None

        return render_template('report.html',
            result_image=result_image,
            predicted_class=predicted_class,
            confidence=confidence,
            x1=x1, y1=y1, x2=x2, y2=y2,
            width=width, height=height,
            mask_area=mask_area,
            patient_info=patient_info,
            processing_time=processing_time,
            analysis_date=datetime.now().strftime('%d %b %Y'),
            scan_time=datetime.now().strftime('%H:%M')
        )

    except Exception as e:
        print(f"❌ Error in handle_new_analysis: {e}")
        flash(f'❌ Analysis error: {str(e)}', 'error')
        return redirect(url_for('upload'))

def view_existing_result(result_id):
    """View an existing analysis result and all results for that patient"""
    connection = get_db_connection()
    if not connection:
        flash('❌ Database connection failed', 'error')
        return redirect(url_for('upload'))

    try:
        cursor = connection.cursor(dictionary=True)

        # Get the specific analysis result with patient info
        query = """
        SELECT r.*, p.gender, p.age, p.date_of_birth, p.radiologist, p.created_at as patient_created_at
        FROM images_result r
        LEFT JOIN patients p ON r.patient_id = p.patient_id
        WHERE r.id = %s
        """
        cursor.execute(query, (result_id,))
        main_result = cursor.fetchone()

        if not main_result:
            flash(f'❌ Analysis result not found: {result_id}', 'error')
            return redirect(url_for('history'))

        # Get ALL analysis results for this patient
        patient_id = main_result['patient_id']
        all_results_query = """
        SELECT r.*, p.gender, p.age, p.date_of_birth, p.radiologist, p.created_at as patient_created_at
        FROM images_result r
        LEFT JOIN patients p ON r.patient_id = p.patient_id
        WHERE r.patient_id = %s
        ORDER BY r.scan_date DESC
        """
        cursor.execute(all_results_query, (patient_id,))
        all_patient_results = cursor.fetchall()

        # Get all unique tumor types for this patient (excluding "No tumor" cases)
        tumor_types_query = """
        SELECT DISTINCT predicted_class, COUNT(*) as count
        FROM images_result
        WHERE patient_id = %s
          AND predicted_class != 'no_tumour'
          AND predicted_class != 'No tumor'
          AND LOWER(predicted_class) != 'no tumor'
        GROUP BY predicted_class
        ORDER BY count DESC, predicted_class
        """
        cursor.execute(tumor_types_query, (patient_id,))
        tumor_types_raw = cursor.fetchall()

        # Format tumor types list for display
        patient_tumor_types = []
        for i, tumor_type in enumerate(tumor_types_raw, 1):
            tumor_name = tumor_type['predicted_class'].replace('_', ' ').title()
            patient_tumor_types.append(f"{i}) {tumor_name}")

        # Determine overall patient diagnosis
        has_tumors = len(patient_tumor_types) > 0
        overall_diagnosis = "Positive" if has_tumors else "Negative"

        # Format patient info
        patient_info = {
            'patient_id': main_result['patient_id'],
            'patient_name': main_result['patient_name'],
            'gender': main_result.get('gender', 'Unknown'),
            'age': main_result.get('age', 'Unknown'),
            'date_of_birth': main_result.get('date_of_birth'),
            'radiologist': main_result.get('radiologist', 'Unknown')
        }

        # Process all results for image display - ONLY SHOW PATIENT-SPECIFIC IMAGES
        processed_results = []
        for result in all_patient_results:
            # Extract filename from result_image (handle cases with folder paths)
            result_filename = result['result_image']
            if '/' in result_filename:
                result_filename = result_filename.split('/')[-1]

            # ONLY check patient-specific result folder - no fallbacks to UNKNOWN
            patient_result_path = f'results/result_{patient_id}/' + result_filename
            patient_result_path_with_prefix = f'results/result_{patient_id}/result_' + result_filename

            # Check if patient-specific result image exists
            if os.path.exists(os.path.join('static', patient_result_path)):
                image_path = patient_result_path
                print(f"✅ Found patient-specific result: {patient_result_path}")
            elif os.path.exists(os.path.join('static', patient_result_path_with_prefix)):
                image_path = patient_result_path_with_prefix
                print(f"✅ Found patient-specific result with prefix: {patient_result_path_with_prefix}")
            else:
                # Skip this result if no patient-specific image exists
                print(f"❌ Skipping result - no patient-specific image found for: {result_filename}")
                continue

            processed_results.append({
                'id': result['id'],
                'result_image': result['result_image'],
                'image_path': image_path,
                'predicted_class': result['predicted_class'],
                'confidence': result['confidence'],
                'x1': result['x1'], 'y1': result['y1'],
                'x2': result['x2'], 'y2': result['y2'],
                'width': result['width'], 'height': result['height'],
                'mask_area': result['mask_area'],
                'scan_date': result['scan_date'],
                'processing_time': result.get('processing_time', 0)
            })



        return render_template('report.html',
            # Main result data (for backward compatibility)
            result_image=main_result['result_image'],
            predicted_class=main_result['predicted_class'],
            confidence=main_result['confidence'],
            x1=main_result['x1'], y1=main_result['y1'], x2=main_result['x2'], y2=main_result['y2'],
            width=main_result['width'], height=main_result['height'],
            mask_area=main_result['mask_area'],
            processing_time=main_result.get('processing_time', 0),
            analysis_date=main_result['scan_date'].strftime('%d %b %Y') if main_result['scan_date'] else 'Unknown',
            scan_time=main_result['scan_date'].strftime('%H:%M') if main_result['scan_date'] else 'Unknown',
            # New: All results for image gallery
            all_results=processed_results,
            patient_info=patient_info,
            # Google Drive image_url/use_drive_image removed
            # New: Multiple tumor types support
            patient_tumor_types=patient_tumor_types,
            has_multiple_tumors=len(patient_tumor_types) > 1,
            overall_diagnosis=overall_diagnosis,
            total_tumor_types=len(patient_tumor_types)
        )

    except mysql.connector.Error as e:
        print(f"❌ Database error in view_existing_result: {e}")
        flash(f'❌ Database error: {str(e)}', 'error')
        return redirect(url_for('history'))
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def view_patient_latest_result(patient_id):
    """View all analysis results for a patient"""
    connection = get_db_connection()
    if not connection:
        flash('❌ Database connection failed', 'error')
        return redirect(url_for('upload'))

    try:
        cursor = connection.cursor(dictionary=True)

        # Get ALL analysis results for patient (not just latest)
        query = """
        SELECT r.*, p.gender, p.age, p.date_of_birth, p.radiologist, p.created_at as patient_created_at
        FROM images_result r
        LEFT JOIN patients p ON r.patient_id = p.patient_id
        WHERE r.patient_id = %s
        ORDER BY r.scan_date DESC
        """
        cursor.execute(query, (patient_id,))
        results = cursor.fetchall()

        if not results:
            flash(f'❌ No analysis results found for patient: {patient_id}', 'error')
            return redirect(url_for('upload'))

        # Use first result for patient info and main analysis data
        main_result = results[0]

        # Format patient info
        patient_info = {
            'patient_id': main_result['patient_id'],
            'patient_name': main_result['patient_name'],
            'gender': main_result.get('gender', 'Unknown'),
            'age': main_result.get('age', 'Unknown'),
            'date_of_birth': main_result.get('date_of_birth'),
            'radiologist': main_result.get('radiologist', 'Unknown')
        }

        # Process all results for image display - ONLY SHOW PATIENT-SPECIFIC IMAGES
        processed_results = []
        for result in results:
            # Extract filename from result_image (handle cases with folder paths)
            result_filename = result['result_image']
            if '/' in result_filename:
                result_filename = result_filename.split('/')[-1]

            # ONLY check patient-specific result folder - no fallbacks to UNKNOWN
            patient_result_path = f'results/result_{patient_id}/' + result_filename
            patient_result_path_with_prefix = f'results/result_{patient_id}/result_' + result_filename

            # Check if patient-specific result image exists
            if os.path.exists(os.path.join('static', patient_result_path)):
                image_path = patient_result_path
                print(f"✅ Found patient-specific result: {patient_result_path}")
            elif os.path.exists(os.path.join('static', patient_result_path_with_prefix)):
                image_path = patient_result_path_with_prefix
                print(f"✅ Found patient-specific result with prefix: {patient_result_path_with_prefix}")
            else:
                # Skip this result if no patient-specific image exists
                print(f"❌ Skipping result - no patient-specific image found for: {result_filename}")
                continue

            processed_results.append({
                'id': result['id'],
                'result_image': result['result_image'],
                'image_path': image_path,
                'predicted_class': result['predicted_class'],
                'confidence': result['confidence'],
                'x1': result['x1'], 'y1': result['y1'],
                'x2': result['x2'], 'y2': result['y2'],
                'width': result['width'], 'height': result['height'],
                'mask_area': result['mask_area'],
                'scan_date': result['scan_date'],
                'processing_time': result.get('processing_time', 0)
            })

        # Get all unique tumor types for this patient (excluding "No tumor" cases)
        tumor_types_query = """
        SELECT DISTINCT predicted_class, COUNT(*) as count
        FROM images_result
        WHERE patient_id = %s
          AND predicted_class != 'no_tumour'
          AND predicted_class != 'No tumor'
          AND LOWER(predicted_class) != 'no tumor'
        GROUP BY predicted_class
        ORDER BY count DESC, predicted_class
        """
        cursor.execute(tumor_types_query, (patient_id,))
        tumor_types_raw = cursor.fetchall()

        # Format tumor types list for display
        patient_tumor_types = []
        for i, tumor_type in enumerate(tumor_types_raw, 1):
            tumor_name = tumor_type['predicted_class'].replace('_', ' ').title()
            patient_tumor_types.append(f"{i}) {tumor_name}")

        # Determine overall patient diagnosis
        has_tumors = len(patient_tumor_types) > 0
        overall_diagnosis = "Positive" if has_tumors else "Negative"

        return render_template('report.html',
            # Main result data (for backward compatibility)
            result_image=main_result['result_image'],
            predicted_class=main_result['predicted_class'],
            confidence=main_result['confidence'],
            x1=main_result['x1'], y1=main_result['y1'], x2=main_result['x2'], y2=main_result['y2'],
            width=main_result['width'], height=main_result['height'],
            mask_area=main_result['mask_area'],
            processing_time=main_result.get('processing_time', 0),
            analysis_date=main_result['scan_date'].strftime('%d %b %Y') if main_result['scan_date'] else 'Unknown',
            scan_time=main_result['scan_date'].strftime('%H:%M') if main_result['scan_date'] else 'Unknown',
            # New: All results for image gallery
            all_results=processed_results,
            patient_info=patient_info,
            # New: Multiple tumor types support
            patient_tumor_types=patient_tumor_types,
            has_multiple_tumors=len(patient_tumor_types) > 1,
            overall_diagnosis=overall_diagnosis,
            total_tumor_types=len(patient_tumor_types)
        )

    except mysql.connector.Error as e:
        print(f"❌ Database error in view_patient_latest_result: {e}")
        flash(f'❌ Database error: {str(e)}', 'error')
        return redirect(url_for('upload'))
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def get_patient_info(patient_id):
    """Get patient information from database"""
    connection = get_db_connection()
    if not connection:
        return None

    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT * FROM patients WHERE patient_id = %s", (patient_id,))
        return cursor.fetchone()
    except mysql.connector.Error as e:
        print(f"❌ Database error in get_patient_info: {e}")
        return None
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def show_report_dashboard():
    """Show report dashboard with consolidated patient analysis results"""
    connection = get_db_connection()
    if not connection:
        flash('❌ Database connection failed', 'error')
        return redirect(url_for('upload'))

    try:
        cursor = connection.cursor(dictionary=True)

        # Get basic patient info with analysis summary
        query = """
        SELECT
            p.patient_id,
            p.patient_name,
            p.radiologist,
            MAX(r.scan_date) as latest_analysis_date,
            COUNT(r.id) as total_scans,

            -- Determine overall result (Positive if any tumor detected, Negative if all no_tumor)
            CASE
                WHEN SUM(CASE WHEN r.predicted_class != 'no_tumour' AND r.predicted_class != 'No tumor' THEN 1 ELSE 0 END) > 0
                THEN 'Positive'
                ELSE 'Negative'
            END as overall_result

        FROM patients p
        LEFT JOIN images_result r ON p.patient_id = r.patient_id
        WHERE r.id IS NOT NULL  -- Only patients with analysis results
        GROUP BY p.patient_id, p.patient_name, p.radiologist
        ORDER BY latest_analysis_date DESC
        LIMIT 20
        """
        cursor.execute(query)
        consolidated_results = cursor.fetchall()

        # For each patient, get detailed tumor types and all confidence scores
        for result in consolidated_results:
            patient_id = result['patient_id']

            # Get all unique tumor types for this patient (excluding no_tumor)
            tumor_query = """
            SELECT DISTINCT predicted_class
            FROM images_result
            WHERE patient_id = %s
              AND predicted_class != 'no_tumour'
              AND predicted_class != 'No tumor'
            ORDER BY predicted_class
            """
            cursor.execute(tumor_query, (patient_id,))
            tumor_types = cursor.fetchall()

            # Format tumor types list
            if tumor_types:
                tumor_list = []
                for i, tumor in enumerate(tumor_types, 1):
                    formatted_tumor = tumor['predicted_class'].replace('_', ' ').title()
                    tumor_list.append(f"{i}) {formatted_tumor}")
                result['tumor_types_list'] = tumor_list
                result['tumor_type_display'] = '\n'.join(tumor_list)
            else:
                result['tumor_types_list'] = []
                result['tumor_type_display'] = 'N/A'

            # Get ALL confidence scores for this patient (from all processed images)
            confidence_query = """
            SELECT predicted_class, confidence, scan_date
            FROM images_result
            WHERE patient_id = %s
            ORDER BY scan_date ASC
            """
            cursor.execute(confidence_query, (patient_id,))
            all_confidences = cursor.fetchall()

            # Format confidence scores list
            confidence_list = []
            for i, conf in enumerate(all_confidences, 1):
                tumor_name = conf['predicted_class'].replace('_', ' ').title()
                if conf['predicted_class'] in ['no_tumour', 'No tumor']:
                    tumor_name = 'No Tumor'
                confidence_pct = f"{conf['confidence']:.1%}"
                confidence_list.append({
                    'index': i,
                    'tumor_name': tumor_name,
                    'confidence': conf['confidence'],
                    'confidence_pct': confidence_pct,
                    'scan_date': conf['scan_date']
                })

            result['confidence_scores_list'] = confidence_list
            result['total_confidence_scores'] = len(confidence_list)

            # Format analysis date
            if result['latest_analysis_date']:
                result['analysis_date_formatted'] = result['latest_analysis_date'].strftime('%d %b %Y')
            else:
                result['analysis_date_formatted'] = 'Unknown'

        # Get summary statistics
        stats_query = """
        SELECT
            COUNT(*) as total_analyses,
            COUNT(DISTINCT patient_id) as total_patients,
            SUM(CASE WHEN predicted_class != 'no_tumour' AND predicted_class != 'No tumor' THEN 1 ELSE 0 END) as positive_cases,
            AVG(confidence) as avg_confidence
        FROM images_result
        """
        cursor.execute(stats_query)
        stats = cursor.fetchone()

        return render_template('report_dashboard.html',
            recent_results=consolidated_results,
            stats=stats
        )

    except mysql.connector.Error as e:
        print(f"❌ Database error in show_report_dashboard: {e}")
        flash(f'❌ Database error: {str(e)}', 'error')
        return redirect(url_for('upload'))
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/report/<int:result_id>')
def report_by_id(result_id):
    """View report by result ID"""
    try:
        return view_existing_result(result_id)
    except Exception as e:
        print(f"❌ Error in report_by_id: {e}")
        # Fallback: try to show a demo report with available images
        return create_demo_report(result_id)

@app.route('/patient/<patient_id>/report')
def patient_report(patient_id):
    """View latest report for a patient"""
    try:
        return view_patient_latest_result(patient_id)
    except Exception as e:
        print(f"❌ Error in patient_report: {e}")
        # Fallback: create demo report for this patient
        return create_patient_demo_report(patient_id)

@app.route('/demo/report/<patient_id>')
def demo_patient_report(patient_id):
    """Create a demo report for a specific patient using available images"""
    return create_patient_demo_report(patient_id)

@app.route('/raw_image/<path:filename>')
def serve_raw_image(filename):
    """Serve raw uploaded images from the uploads folder based on database records"""
    print(f"🖼️ Serving raw image: {filename}")
    try:
        # First, try to find which patient this file belongs to by checking the database
        connection = get_db_connection()
        if connection:
            try:
                cursor = connection.cursor(dictionary=True)
                # Look for the file in the uploads table to get the patient_id and file_path
                query = "SELECT patient_id, file_path, file_name FROM uploads WHERE file_name = %s"
                cursor.execute(query, (filename,))
                result = cursor.fetchone()
                print(f"📊 Database lookup result: {result}")

                if result:
                    patient_id = result['patient_id']
                    stored_path = result['file_path']
                    db_filename = result['file_name']

                    print(f"🔍 Patient: {patient_id}")
                    print(f"🔍 DB filename: {db_filename}")
                    print(f"🔍 Stored path: {stored_path}")

                    # Extract the base filename pattern (without timestamp)
                    # From: P0001_Tr-me_0001_jpg.rf.27b12f7c0e2b6e12a70e5fcc0e483b90_6792b959_1752581598.jpg
                    # To: P0001_Tr-me_0001_jpg.rf.27b12f7c0e2b6e12a70e5fcc0e483b90
                    base_filename = stored_path.split('\\')[-1]  # Get filename from path
                    if '_' in base_filename:
                        # Remove the last two underscore parts (timestamp parts)
                        parts = base_filename.split('_')
                        if len(parts) >= 3:
                            base_pattern = '_'.join(parts[:-2])  # Remove last 2 parts
                            print(f"🔍 Base pattern: {base_pattern}")

                            # Search in both upload directories
                            search_dirs = [
                                os.path.join('uploads', patient_id),
                                os.path.join('static', 'uploads', patient_id)
                            ]

                            for search_dir in search_dirs:
                                if os.path.exists(search_dir):
                                    print(f"🔍 Searching in: {search_dir}")
                                    for file in os.listdir(search_dir):
                                        if file.startswith(base_pattern) and file.endswith('.jpg'):
                                            found_path = os.path.join(search_dir, file)
                                            print(f"✅ Found matching file: {found_path}")
                                            return send_file(found_path)

                    print(f"⚠️ No matching file found for pattern: {filename}")

            except mysql.connector.Error as e:
                print(f"❌ Database error in serve_raw_image: {e}")
            finally:
                if connection.is_connected():
                    cursor.close()
                    connection.close()

        # Fallback: Try different possible locations for the raw image
        possible_paths = [
            os.path.join('static', 'uploads', filename),
            os.path.join('static', 'uploads', 'raw', filename),
            os.path.join('uploads', filename)
        ]

        for image_path in possible_paths:
            if os.path.exists(image_path):
                print(f"✅ Serving raw image from fallback: {image_path}")
                return send_file(image_path)

        print(f"❌ Raw image not found: {filename}")
        return "Raw image not found", 404

    except Exception as e:
        print(f"❌ Error serving raw image: {str(e)}")
        return "Error serving raw image", 500

@app.route('/patient_image/<patient_id>/<path:filename>')
def serve_patient_image(patient_id, filename):
    """Serve images from external patient directory"""
    try:
        # Handle cases where filename might include folder path (e.g., "result_P0006/image.jpg")
        if '/' in filename:
            # Extract just the filename part
            actual_filename = filename.split('/')[-1]
            print(f"🔧 Extracted filename: {actual_filename} from path: {filename}")
        else:
            actual_filename = filename

        # FIXED: Handle database paths that already include result_XXXX/ prefix
        possible_paths = [
            # HIGHEST PRIORITY: If filename already contains result_XXXX/ path, use it directly under static/results/
            os.path.join('static', 'results', filename) if ('/' in filename and f'result_{patient_id}' in filename) else None,
            # Static results (patient-specific) - for files without path prefix
            os.path.join('static', 'results', f'result_{patient_id}', actual_filename),
            # Static results (patient-specific) with result_ prefix
            os.path.join('static', 'results', f'result_{patient_id}', f'result_{actual_filename}'),
            # Static uploads patient-specific folder (original uploaded files)
            os.path.join('static', 'uploads', patient_id, actual_filename),
            # External patient directory (original images)
            os.path.join(EXTERNAL_PATIENT_DIR, patient_id, actual_filename)
        ]

        # Remove None entries
        possible_paths = [p for p in possible_paths if p is not None]

        print(f"🔍 Looking for PROCESSED image: {filename} (actual: {actual_filename}) for patient: {patient_id}")

        # First, try exact matches
        for image_path in possible_paths:
            if os.path.exists(image_path):
                print(f"✅ Found exact match, serving image from: {image_path}")
                return send_file(image_path)

        print(f"❌ Image not found in any location: {filename}")
        print(f"   Checked paths:")
        for path in possible_paths:
            print(f"   - {path} (exists: {os.path.exists(path)})")

        # Try to find any similar files in result directories
        print(f"🔍 Searching for similar files...")
        for result_dir in ['static/results', f'static/results/result_{patient_id}', 'static/results/result_UNKNOWN']:
            if os.path.exists(result_dir):
                files = os.listdir(result_dir)
                similar_files = [f for f in files if actual_filename.lower() in f.lower() or f.lower() in actual_filename.lower()]
                if similar_files:
                    print(f"   📁 {result_dir}: Found similar files: {similar_files}")
                    # Try to serve the first similar file
                    similar_path = os.path.join(result_dir, similar_files[0])
                    if os.path.exists(similar_path):
                        print(f"✅ Serving similar image: {similar_path}")
                        return send_file(similar_path)

        # Try to find any file with similar name ONLY in patient-specific directories
        print(f"🔍 Searching for similar files in patient-specific directories only...")
        search_dirs = [
            os.path.join('static', 'results', f'result_{patient_id}'),
            os.path.join(EXTERNAL_PATIENT_DIR, patient_id),
            os.path.join('static', 'uploads', patient_id)
        ]

        for search_dir in search_dirs:
            if os.path.exists(search_dir):
                files = os.listdir(search_dir)
                print(f"   Files in {search_dir}: {files[:5]}...")  # Show first 5 files

                # Look for files that contain the base filename (more flexible matching)
                base_filename = actual_filename.replace('result_', '')
                for file in files:
                    # Try multiple matching strategies
                    if (actual_filename in file or
                        base_filename in file or
                        file in actual_filename or
                        actual_filename.replace('.jpg', '') in file):
                        found_path = os.path.join(search_dir, file)
                        print(f"✅ Found similar file: {found_path}")
                        return send_file(found_path)

        # Return a placeholder image if available, otherwise 404
        placeholder_path = os.path.join('static', 'images', 'RadioLens_Logo.png')
        if os.path.exists(placeholder_path):
            print(f"🖼️ Serving placeholder image: {placeholder_path}")
            return send_file(placeholder_path)

        return "Image not found", 404

    except Exception as e:
        print(f"❌ Error serving patient image: {str(e)}")
        return "Error serving image", 500

@app.route('/debug/external_patients')
def debug_external_patients():
    """Debug route to check external patient directory"""
    try:
        html = f"""
        <h2>🗂️ External Patient Directory Debug</h2>
        <p><strong>External Directory Path:</strong> <code>{EXTERNAL_PATIENT_DIR}</code></p>
        <p><strong>Directory Exists:</strong> {os.path.exists(EXTERNAL_PATIENT_DIR)}</p>
        """

        # Check patient folders
        if os.path.exists(EXTERNAL_PATIENT_DIR):
            patient_folders = [f for f in os.listdir(EXTERNAL_PATIENT_DIR) if os.path.isdir(os.path.join(EXTERNAL_PATIENT_DIR, f))]
            html += f"<p><strong>Patient Folders Found:</strong> {len(patient_folders)}</p>"
            html += f"<p><strong>Sample Folders:</strong> {patient_folders[:10]}</p>"

            # Check a specific patient folder (P0007 from your screenshot)
            p0007_path = os.path.join(EXTERNAL_PATIENT_DIR, 'P0007')
            if os.path.exists(p0007_path):
                p0007_files = os.listdir(p0007_path)
                html += f"<h3>📁 P0007 Folder Contents:</h3>"
                html += f"<p><strong>Files found:</strong> {len(p0007_files)}</p>"
                html += f"<ul>"
                for file in p0007_files:
                    html += f"<li>{file}</li>"
                html += f"</ul>"

                # Test image serving for P0007
                if p0007_files:
                    test_file = p0007_files[0]
                    html += f"<h3>🖼️ Test Image Display:</h3>"
                    html += f"<p>Testing file: {test_file}</p>"
                    html += f'<img src="/patient_image/P0007/{test_file}" style="max-width: 300px; border: 1px solid #ccc;" alt="Test P0007 Image">'

            # Check each patient folder
            for folder in patient_folders[:3]:  # Check first 3 folders
                folder_path = os.path.join(EXTERNAL_PATIENT_DIR, folder)
                files = os.listdir(folder_path)
                html += f"<p><strong>{folder}:</strong> {len(files)} files</p>"
                html += f"<ul>"
                for file in files[:5]:  # Show first 5 files
                    html += f"<li>{file}</li>"
                html += f"</ul>"

        # Check static results
        html += f"<h3>Static Results Directory</h3>"
        results_dir = os.path.join('static', 'results')
        if os.path.exists(results_dir):
            result_folders = os.listdir(results_dir)
            html += f"<p><strong>Result Folders:</strong> {result_folders}</p>"

            # Check UNKNOWN folder
            unknown_dir = os.path.join(results_dir, 'result_UNKNOWN')
            if os.path.exists(unknown_dir):
                unknown_files = os.listdir(unknown_dir)
                html += f"<p><strong>UNKNOWN folder:</strong> {len(unknown_files)} files</p>"
                html += f"<ul>"
                for file in unknown_files[:10]:  # Show first 10 files
                    html += f"<li>{file}</li>"
                html += f"</ul>"

        html += f"</body></html>"
        return html
    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/debug/test_image/<patient_id>/<filename>')
def debug_test_image(patient_id, filename):
    """Debug route to test image serving"""
    try:
        html = f"""
        <h2>🖼️ Image Serving Debug</h2>
        <p><strong>Patient ID:</strong> {patient_id}</p>
        <p><strong>Filename:</strong> {filename}</p>
        """

        # Check all possible paths
        possible_paths = [
            os.path.join(EXTERNAL_PATIENT_DIR, patient_id, filename),
            os.path.join('static', 'results', f'result_{patient_id}', filename),
            os.path.join('static', 'results', 'result_UNKNOWN', f'result_{filename}'),
            os.path.join('static', 'results', 'result_UNKNOWN', filename),
            os.path.join('static', 'results', filename),
            os.path.join('static', 'uploads', filename),
            os.path.join('static', 'uploads', patient_id, filename)
        ]

        html += "<h3>Path Check Results:</h3><ul>"
        for path in possible_paths:
            exists = os.path.exists(path)
            html += f"<li><strong>{path}</strong>: {'✅ EXISTS' if exists else '❌ NOT FOUND'}</li>"
        html += "</ul>"

        # Try to display the image if found
        for path in possible_paths:
            if os.path.exists(path):
                html += f"<h3>Found Image:</h3>"
                html += f"<p>Path: {path}</p>"
                html += f'<img src="/patient_image/{patient_id}/{filename}" style="max-width: 300px;" alt="Test Image">'
                break

        return html
    except Exception as e:
        return f"Error: {str(e)}"

@app.route('/debug/report/<int:result_id>')
def debug_report_data(result_id):
    """Debug route to check report data and image paths"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get the specific result
        cursor.execute("SELECT * FROM images_result WHERE id = %s", (result_id,))
        result = cursor.fetchone()

        if not result:
            return f"❌ No result found with ID: {result_id}"

        patient_id = result['patient_id']
        result_image = result['result_image']

        html = f"<h2>🔍 Debug Report Data for Result ID: {result_id}</h2>"
        html += f"<p><strong>Patient ID:</strong> {patient_id}</p>"
        html += f"<p><strong>Result Image:</strong> {result_image}</p>"
        html += f"<p><strong>Predicted Class:</strong> {result['predicted_class']}</p>"
        html += f"<p><strong>Confidence:</strong> {result['confidence']}</p>"

        # Check all possible image paths for PROCESSED RESULT IMAGES
        possible_paths = [
            # Primary: Patient-specific result folder (where processed images should be)
            os.path.join('static', 'results', f'result_{patient_id}', result_image),
            # Fallback: General result folder
            os.path.join('static', 'results', result_image),
            # Fallback: Unknown result folder
            os.path.join('static', 'results', 'result_UNKNOWN', result_image),
            # Alternative: Check if it's in the main results folder with patient prefix
            os.path.join('static', 'results', f'{patient_id}_{result_image}'),
            # Alternative: Check if it's in the main results folder with result prefix
            os.path.join('static', 'results', f'result_{result_image}'),
        ]

        html += "<h3>Image Path Check:</h3><ul>"
        found_path = None
        for path in possible_paths:
            exists = os.path.exists(path)
            html += f"<li><strong>{path}</strong>: {'✅ EXISTS' if exists else '❌ NOT FOUND'}</li>"
            if exists and not found_path:
                found_path = path
        html += "</ul>"

        # Show the image if found
        if found_path:
            html += f"<h3>✅ Image Found:</h3>"
            html += f"<p>Path: {found_path}</p>"
            html += f'<img src="/patient_image/{patient_id}/{result_image}" style="max-width: 400px; border: 1px solid #ccc;" alt="Result Image">'
        else:
            html += f"<h3>❌ Image Not Found</h3>"
            html += f"<p>The image <code>{result_image}</code> for patient <code>{patient_id}</code> could not be found in any expected location.</p>"

        # Add links for further debugging
        html += f"<h3>🔗 Debug Links:</h3>"
        html += f"<p><a href='/debug/test_image/{patient_id}/{result_image}'>🔍 Check Image Paths</a></p>"
        html += f"<p><a href='/debug/patient/{patient_id}'>👤 Check Patient Data</a></p>"
        html += f"<p><a href='/report/{result_id}'>📊 View Original Report</a></p>"

        return html

    except Exception as e:
        return f"❌ Error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/sync_patient_images/<patient_id>')
def sync_patient_images(patient_id):
    """Sync images from external patient directory to static uploads folder"""
    try:
        import shutil

        # Source: external patient directory
        source_dir = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
        # Destination: static uploads patient folder
        dest_dir = os.path.join('static', 'uploads', patient_id)

        if not os.path.exists(source_dir):
            return f"❌ Source directory not found: {source_dir}"

        # Create destination directory
        os.makedirs(dest_dir, exist_ok=True)

        # Copy all image files
        copied_files = []
        skipped_files = []

        for filename in os.listdir(source_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff')):
                source_file = os.path.join(source_dir, filename)
                dest_file = os.path.join(dest_dir, filename)

                try:
                    shutil.copy2(source_file, dest_file)
                    copied_files.append(filename)
                    print(f"✅ Copied: {filename}")
                except Exception as e:
                    skipped_files.append(f"{filename} (Error: {e})")
                    print(f"❌ Failed to copy {filename}: {e}")

        result_html = f"""
        <h2>🔄 Patient Image Sync Results</h2>
        <p><strong>Patient ID:</strong> {patient_id}</p>
        <p><strong>Source:</strong> {source_dir}</p>
        <p><strong>Destination:</strong> {dest_dir}</p>
        <p><strong>Copied Files:</strong> {len(copied_files)}</p>
        <p><strong>Skipped Files:</strong> {len(skipped_files)}</p>

        <h3>✅ Successfully Copied:</h3>
        <ul>
        """

        for file in copied_files:
            result_html += f"<li>{file}</li>"

        result_html += "</ul>"

        if skipped_files:
            result_html += "<h3>❌ Skipped Files:</h3><ul>"
            for file in skipped_files:
                result_html += f"<li>{file}</li>"
            result_html += "</ul>"

        result_html += f'<p><a href="/demo/report/{patient_id}">📊 View Patient Report</a></p>'

        return result_html

    except Exception as e:
        return f"❌ Error syncing patient images: {str(e)}"

@app.route('/sync_all_patient_images')
def sync_all_patient_images():
    """Sync images for all patients from external directory to static uploads folder"""
    try:
        import shutil

        if not os.path.exists(EXTERNAL_PATIENT_DIR):
            return f"❌ External patient directory not found: {EXTERNAL_PATIENT_DIR}"

        # Get all patient folders
        patient_folders = [f for f in os.listdir(EXTERNAL_PATIENT_DIR)
                          if os.path.isdir(os.path.join(EXTERNAL_PATIENT_DIR, f))]

        if not patient_folders:
            return f"❌ No patient folders found in: {EXTERNAL_PATIENT_DIR}"

        results = []
        total_copied = 0
        total_skipped = 0

        for patient_id in patient_folders:
            # Source: external patient directory
            source_dir = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
            # Destination: static uploads patient folder
            dest_dir = os.path.join('static', 'uploads', patient_id)

            # Create destination directory
            os.makedirs(dest_dir, exist_ok=True)

            # Copy all image files
            copied_files = []
            skipped_files = []

            for filename in os.listdir(source_dir):
                if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff')):
                    source_file = os.path.join(source_dir, filename)
                    dest_file = os.path.join(dest_dir, filename)

                    try:
                        # Only copy if file doesn't exist or is different
                        if not os.path.exists(dest_file) or os.path.getmtime(source_file) > os.path.getmtime(dest_file):
                            shutil.copy2(source_file, dest_file)
                            copied_files.append(filename)
                            print(f"✅ Copied: {patient_id}/{filename}")
                        else:
                            print(f"⏭️ Skipped (already exists): {patient_id}/{filename}")
                    except Exception as e:
                        skipped_files.append(f"{filename} (Error: {e})")
                        print(f"❌ Failed to copy {patient_id}/{filename}: {e}")

            results.append({
                'patient_id': patient_id,
                'copied': len(copied_files),
                'skipped': len(skipped_files),
                'copied_files': copied_files,
                'skipped_files': skipped_files
            })

            total_copied += len(copied_files)
            total_skipped += len(skipped_files)

        # Generate HTML report
        result_html = f"""
        <h2>🔄 All Patient Images Sync Results</h2>
        <p><strong>External Directory:</strong> {EXTERNAL_PATIENT_DIR}</p>
        <p><strong>Destination:</strong> static/uploads/</p>
        <p><strong>Patients Processed:</strong> {len(patient_folders)}</p>
        <p><strong>Total Files Copied:</strong> {total_copied}</p>
        <p><strong>Total Files Skipped:</strong> {total_skipped}</p>

        <h3>📊 Per-Patient Results:</h3>
        """

        for result in results:
            result_html += f"""
            <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                <h4>Patient {result['patient_id']}</h4>
                <p>✅ Copied: {result['copied']} files | ❌ Skipped: {result['skipped']} files</p>
                <p><a href="/demo/report/{result['patient_id']}">📊 View Report</a></p>
            </div>
            """

        result_html += """
        <h3>🔗 Quick Links:</h3>
        <ul>
            <li><a href="/demo/report/P0000">📊 P0000 Report</a></li>
            <li><a href="/demo/report/P0001">📊 P0001 Report</a></li>
            <li><a href="/demo/report/P0002">📊 P0002 Report</a></li>
            <li><a href="/demo/report/P0003">📊 P0003 Report</a></li>
        </ul>
        """

        return result_html

    except Exception as e:
        return f"❌ Error syncing all patient images: {str(e)}"

def create_demo_report(result_id):
    """Create a demo report when database is not available"""
    try:
        # Try to find patient folders and images
        patient_folders = []
        if os.path.exists(EXTERNAL_PATIENT_DIR):
            patient_folders = [f for f in os.listdir(EXTERNAL_PATIENT_DIR)
                             if os.path.isdir(os.path.join(EXTERNAL_PATIENT_DIR, f))]

        # Use P0000 as default or first available patient
        patient_id = 'P0000' if 'P0000' in patient_folders else (patient_folders[0] if patient_folders else 'UNKNOWN')

        # Get images from patient folder
        patient_images = []
        patient_folder_path = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
        if os.path.exists(patient_folder_path):
            all_files = os.listdir(patient_folder_path)
            patient_images = [f for f in all_files if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        # Create mock results for display
        mock_results = []
        tumor_types = ['Glioma Tumor', 'Meningioma Tumor', 'Pituitary Tumor', 'No Tumor']

        for i, image_file in enumerate(patient_images[:4]):  # Show up to 4 images
            mock_results.append({
                'id': i + 1,
                'result_image': image_file,  # Use original image as result image
                'image_path': f'patient_images/{patient_id}/{image_file}',
                'predicted_class': tumor_types[i % len(tumor_types)],
                'confidence': 0.85 + (i * 0.03),  # Mock confidence values
                'x1': 50, 'y1': 50, 'x2': 200, 'y2': 200,
                'width': 150, 'height': 150,
                'mask_area': 22500,
                'scan_date': datetime.now(),
                'processing_time': 2.5 + (i * 0.2)
            })

        # Create mock patient info
        patient_info = {
            'patient_id': patient_id,
            'patient_name': 'Hans',
            'gender': 'Male',
            'age': 21,
            'date_of_birth': '25 Oct 2003',
            'radiologist': 'Dr. Miklay'
        }

        # If we have results, use the first one as main result
        if mock_results:
            main_result = mock_results[0]
            return render_template('report.html',
                # Main result data
                result_image=main_result['result_image'],
                predicted_class=main_result['predicted_class'],
                confidence=main_result['confidence'],
                x1=main_result['x1'], y1=main_result['y1'],
                x2=main_result['x2'], y2=main_result['y2'],
                width=main_result['width'], height=main_result['height'],
                mask_area=main_result['mask_area'],
                processing_time=main_result['processing_time'],
                analysis_date=datetime.now().strftime('%d %b %Y'),
                scan_time=datetime.now().strftime('%H:%M'),
                # All results for gallery
                all_results=mock_results,
                patient_info=patient_info,
                demo_mode=True
            )
        else:
            # No images found, show error
            flash('❌ No patient images found for display', 'error')
            return redirect(url_for('upload'))

    except Exception as e:
        print(f"❌ Error in create_demo_report: {e}")
        flash(f'❌ Error creating demo report: {str(e)}', 'error')
        return redirect(url_for('upload'))

def create_patient_demo_report(patient_id):
    """Create a demo report for a specific patient using available images"""
    try:
        # Check if patient folder exists
        patient_folder_path = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
        if not os.path.exists(patient_folder_path):
            flash(f'❌ Patient folder not found: {patient_id}', 'error')
            return redirect(url_for('upload'))

        # Get images from patient folder
        all_files = os.listdir(patient_folder_path)
        patient_images = [f for f in all_files if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

        if not patient_images:
            flash(f'❌ No images found for patient: {patient_id}', 'error')
            return redirect(url_for('upload'))

        # Create mock results for display
        mock_results = []
        tumor_types = ['Glioma Tumor', 'Meningioma Tumor', 'Pituitary Tumor', 'No Tumor']
        confidences = [92.3, 94.5, 95.7, 88.1]
        processing_times = [4.56, 3.20, 2.09, 5.12]

        for i, image_file in enumerate(patient_images[:4]):  # Show up to 4 images
            mock_results.append({
                'id': i + 1,
                'result_image': image_file,  # Use original image as result image
                'image_path': f'patient_images/{patient_id}/{image_file}',
                'predicted_class': tumor_types[i % len(tumor_types)],
                'confidence': confidences[i % len(confidences)] / 100.0,
                'x1': 50, 'y1': 50, 'x2': 200, 'y2': 200,
                'width': 150, 'height': 150,
                'mask_area': 22500,
                'scan_date': datetime.now(),
                'processing_time': processing_times[i % len(processing_times)]
            })

        # Create patient info based on patient_id
        patient_names = {
            'P0000': 'Hans',
            'P0001': 'Maria',
            'P0002': 'John',
            'P0003': 'Sarah'
        }

        patient_info = {
            'patient_id': patient_id,
            'patient_name': patient_names.get(patient_id, 'Unknown Patient'),
            'gender': 'Male',
            'age': 21,
            'date_of_birth': datetime(2003, 10, 25),  # Convert to datetime object
            'radiologist': 'Dr. Miklay'
        }

        # Use the first result as main result
        main_result = mock_results[0]

        return render_template('report.html',
            # Main result data
            result_image=main_result['result_image'],
            predicted_class=main_result['predicted_class'],
            confidence=main_result['confidence'],
            x1=main_result['x1'], y1=main_result['y1'],
            x2=main_result['x2'], y2=main_result['y2'],
            width=main_result['width'], height=main_result['height'],
            mask_area=main_result['mask_area'],
            processing_time=main_result['processing_time'],
            analysis_date=datetime.now().strftime('%d %b %Y'),
            scan_time=datetime.now().strftime('%H:%M'),
            # All results for gallery
            all_results=mock_results,
            patient_info=patient_info,
            demo_mode=True
        )

    except Exception as e:
        print(f"❌ Error in create_patient_demo_report: {e}")
        flash(f'❌ Error creating patient demo report: {str(e)}', 'error')
        return redirect(url_for('upload'))

        if os.path.exists(EXTERNAL_PATIENT_DIR):
            html += "<h3>📁 Patient Folders Found:</h3><ul>"
            try:
                for item in os.listdir(EXTERNAL_PATIENT_DIR):
                    item_path = os.path.join(EXTERNAL_PATIENT_DIR, item)
                    if os.path.isdir(item_path):
                        # Count files in patient folder
                        try:
                            files = os.listdir(item_path)
                            file_count = len(files)
                            html += f"<li><strong>{item}</strong> - {file_count} files"
                            if file_count > 0:
                                html += f" ({', '.join(files[:5])}{'...' if file_count > 5 else ''})"
                            html += "</li>"
                        except Exception as e:
                            html += f"<li><strong>{item}</strong> - Error reading folder: {e}</li>"
                html += "</ul>"
            except Exception as e:
                html += f"<p>❌ Error reading directory: {e}</p>"
        else:
            html += "<p>❌ External patient directory does not exist</p>"

        html += """
        <h3>🔗 Test Links:</h3>
        <p>
            <a href="/patient_image/P0001/test.jpg" style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;'>Test P0001 Image</a>
            <a href="/patient_image/P0002/test.jpg" style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>Test P0002 Image</a>
        </p>
        """

        return html

    except Exception as e:
        return f"❌ Error: {str(e)}"

@app.route('/debug/patient/<patient_id>')
def debug_patient_images(patient_id):
    """Debug route to check what images exist for a patient"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT * FROM images_result WHERE patient_id = %s ORDER BY scan_date DESC", (patient_id,))
        results = cursor.fetchall()

        debug_info = f"<h1>Debug Info for Patient {patient_id}</h1>"
        debug_info += f"<h2>Database Results ({len(results)} found):</h2>"

        for i, result in enumerate(results):
            debug_info += f"<h3>Result {i+1}:</h3>"
            debug_info += f"<p><strong>ID:</strong> {result['id']}</p>"
            debug_info += f"<p><strong>Result Image:</strong> {result['result_image']}</p>"
            debug_info += f"<p><strong>Predicted Class:</strong> {result['predicted_class']}</p>"
            debug_info += f"<p><strong>Confidence:</strong> {result['confidence']}</p>"
            debug_info += f"<p><strong>Scan Date:</strong> {result['scan_date']}</p>"

            # Check all possible image paths
            paths_to_check = [
                f'static/results/result_{patient_id}/{result["result_image"]}',
                f'static/results/{result["result_image"]}',
                f'static/results/result_UNKNOWN/{result["result_image"]}'
            ]

            debug_info += "<p><strong>File Path Check:</strong></p><ul>"
            for path in paths_to_check:
                exists = os.path.exists(path)
                status = "✅ EXISTS" if exists else "❌ NOT FOUND"
                debug_info += f"<li>{path} - {status}</li>"
            debug_info += "</ul>"

            debug_info += "<hr>"

        # List all files in results directories
        debug_info += "<h2>Files in Results Directories:</h2>"

        results_dirs = [
            'static/results',
            f'static/results/result_{patient_id}',
            'static/results/result_UNKNOWN'
        ]

        for dir_path in results_dirs:
            debug_info += f"<h3>{dir_path}:</h3>"
            if os.path.exists(dir_path):
                files = os.listdir(dir_path)
                if files:
                    debug_info += "<ul>"
                    for file in files[:10]:  # Show first 10 files
                        debug_info += f"<li>{file}</li>"
                    if len(files) > 10:
                        debug_info += f"<li>... and {len(files) - 10} more files</li>"
                    debug_info += "</ul>"
                else:
                    debug_info += "<p>Directory is empty</p>"
            else:
                debug_info += "<p>Directory does not exist</p>"

        return debug_info

    except Exception as e:
        return f"Error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

# Dashboard route (renamed from overview)
@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')



@app.route('/history')
def history():
    """Display patient-based history (one entry per patient) from database"""
    print("🔍 History route called")
    connection = get_db_connection()
    patients = []
    print(f"📊 Database connection: {'✅ Connected' if connection else '❌ Failed'}")

    if connection:
        try:
            cursor = connection.cursor(dictionary=True)
            # Patient-based query - one entry per patient with aggregated data
            # Total images counted from images_result table by patient_id
            query = """
            SELECT
                p.patient_id,
                p.patient_name,
                p.gender,
                p.age,
                p.radiologist,
                p.created_at as patient_created_date,
                COUNT(r.id) as total_uploads,
                COUNT(r.id) as total_analyses,
                MAX(r.scan_date) as last_upload_date,
                MAX(r.scan_date) as last_analysis_date,
                0 as total_file_size,
                CASE
                    WHEN COUNT(r.id) > 0 THEN 'Analyzed'
                    ELSE 'No Data'
                END as status,
                CASE
                    WHEN SUM(CASE WHEN r.predicted_class LIKE '%tumor%' OR r.predicted_class LIKE '%Tumor%' THEN 1 ELSE 0 END) > 0 THEN 'Positive'
                    WHEN COUNT(r.id) > 0 AND SUM(CASE WHEN r.predicted_class LIKE '%tumor%' OR r.predicted_class LIKE '%Tumor%' THEN 1 ELSE 0 END) = 0 THEN 'Negative'
                    ELSE 'Pending'
                END as overall_diagnosis,
                AVG(r.confidence) as avg_confidence
            FROM patients p
            LEFT JOIN images_result r ON p.patient_id = r.patient_id
            GROUP BY p.patient_id, p.patient_name, p.gender, p.age, p.radiologist, p.created_at
            HAVING COUNT(r.id) > 0
            ORDER BY MAX(r.scan_date) DESC
            """
            cursor.execute(query)
            db_patients = cursor.fetchall()

            # Debug: Print the actual database results
            print("=== DEBUG: History Query Results ===")
            for i, patient_record in enumerate(db_patients[:3]):  # Show first 3 patients
                print(f"Patient {i+1}: {patient_record['patient_id']} - Total Uploads: {patient_record.get('total_uploads', 'N/A')}")
            print("=====================================")

            # Additional debug: Check individual upload counts
            print("=== DEBUG: Individual Upload Counts ===")
            cursor.execute("SELECT patient_id, COUNT(*) as upload_count FROM uploads GROUP BY patient_id ORDER BY upload_count DESC LIMIT 5")
            upload_counts = cursor.fetchall()
            for upload_record in upload_counts:
                print(f"Patient {upload_record['patient_id']}: {upload_record['upload_count']} uploads")
            print("=======================================")

            for patient_record in db_patients:
                # Format confidence score
                confidence_display = ''
                if patient_record.get('avg_confidence'):
                    confidence_display = f"{float(patient_record['avg_confidence']) * 100:.1f}%"

                patients.append({
                    'patient_id': patient_record['patient_id'],
                    'patient_name': patient_record.get('patient_name', 'Unknown'),
                    'gender': patient_record.get('gender', 'N/A'),
                    'age': patient_record.get('age', 'N/A'),
                    'radiologist': patient_record.get('radiologist', 'N/A'),
                    'last_upload_date': patient_record['last_upload_date'].strftime('%d %b %Y %H:%M') if patient_record.get('last_upload_date') else 'N/A',
                    'total_uploads': patient_record.get('total_uploads', 0),
                    'total_analyses': patient_record.get('total_analyses', 0),
                    'total_file_size': format_file_size(patient_record['total_file_size']) if patient_record.get('total_file_size') else 'N/A',
                    'status': patient_record['status'],
                    'diagnosis': patient_record.get('overall_diagnosis', 'Pending'),
                    'confidence': confidence_display,
                    'last_analysis_date': patient_record['last_analysis_date'].strftime('%d %b %Y %H:%M') if patient_record.get('last_analysis_date') else 'N/A',
                    'has_analysis': patient_record.get('total_analyses', 0) > 0
                })
        except mysql.connector.Error as e:
            print(f"Database error in history: {e}")
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    return render_template('history.html', patients=patients)

@app.route('/debug/upload_counts')
def debug_upload_counts():
    """Debug route to check upload counts per patient"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get upload counts per patient
        cursor.execute("""
            SELECT
                p.patient_id,
                p.patient_name,
                COUNT(u.id) as upload_count,
                GROUP_CONCAT(u.file_name SEPARATOR ', ') as file_names
            FROM patients p
            LEFT JOIN uploads u ON p.patient_id = u.patient_id
            GROUP BY p.patient_id, p.patient_name
            ORDER BY upload_count DESC
        """)
        results = cursor.fetchall()

        html = "<h2>🔍 Upload Counts Per Patient</h2>"
        html += "<table border='1' style='border-collapse: collapse; width: 100%;'>"
        html += "<tr style='background: #f0f0f0;'><th>Patient ID</th><th>Patient Name</th><th>Upload Count</th><th>Files</th></tr>"

        for result in results:
            files = result['file_names'][:100] + "..." if result['file_names'] and len(result['file_names']) > 100 else result['file_names']
            html += f"<tr><td>{result['patient_id']}</td><td>{result['patient_name']}</td><td><strong>{result['upload_count']}</strong></td><td>{files or 'No files'}</td></tr>"

        html += "</table>"

        return html

    except mysql.connector.Error as e:
        return f"❌ Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/patient/<patient_id>/raw_images')
def patient_raw_images(patient_id):
    """Display all raw images for a specific patient from uploads table"""
    print(f"🔍 Loading raw images for patient: {patient_id}")
    connection = get_db_connection()
    patient_info = None
    raw_images = []

    if connection:
        try:
            cursor = connection.cursor(dictionary=True)

            # Get patient information
            cursor.execute("SELECT * FROM patients WHERE patient_id = %s", (patient_id,))
            patient_info = cursor.fetchone()
            print(f"📊 Patient info: {patient_info['patient_name'] if patient_info else 'Not found'}")

            if not patient_info:
                flash(f'❌ Patient not found: {patient_id}', 'error')
                return redirect(url_for('history'))

            # Get all uploaded images for this patient with proper original filenames
            query = """
            SELECT
                u.id as upload_id,
                u.file_name as original_filename,
                u.file_path,
                u.file_size,
                u.file_type,
                u.scan_date as upload_date,
                r.id as result_id,
                r.result_image as processed_filename,
                r.predicted_class,
                r.confidence,
                r.scan_date as analysis_date,
                CASE
                    WHEN r.id IS NOT NULL THEN 'Analyzed'
                    ELSE 'Uploaded'
                END as status
            FROM uploads u
            LEFT JOIN images_result r ON u.patient_id = r.patient_id
                AND (r.original_image = u.file_name OR r.result_image LIKE CONCAT('%', u.file_name, '%'))
            WHERE u.patient_id = %s
            ORDER BY u.scan_date DESC
            """
            cursor.execute(query, (patient_id,))
            db_images = cursor.fetchall()
            print(f"📁 Found {len(db_images)} uploaded images in database")

            for image_record in db_images:
                # Format confidence score
                confidence_display = ''
                if image_record.get('confidence'):
                    confidence_display = f"{float(image_record['confidence']) * 100:.1f}%"

                # Debug: Print the processed filename
                processed_filename = image_record.get('processed_filename')
                print(f"🔍 DEBUG - Original: {image_record['original_filename']}")
                print(f"🔍 DEBUG - Processed: {processed_filename}")
                print(f"🔍 DEBUG - Result ID: {image_record.get('result_id')}")
                print(f"🔍 DEBUG - Status: {image_record['status']}")

                raw_images.append({
                    'upload_id': image_record['upload_id'],
                    'filename': image_record['original_filename'],  # Use original filename for raw image viewing
                    'processed_filename': processed_filename,  # Use processed filename for processed image viewing
                    'file_path': image_record['file_path'],
                    'file_size': format_file_size(image_record['file_size']),
                    'file_type': image_record['file_type'],
                    'upload_date': image_record['upload_date'].strftime('%d %b %Y %H:%M'),
                    'status': image_record['status'],
                    'predicted_class': image_record.get('predicted_class', 'N/A'),
                    'confidence': confidence_display,
                    'analysis_date': image_record['analysis_date'].strftime('%d %b %Y %H:%M') if image_record.get('analysis_date') else 'N/A',
                    'result_id': image_record.get('result_id'),
                    'has_analysis': image_record.get('result_id') is not None
                })

        except mysql.connector.Error as e:
            print(f"Database error in patient_raw_images: {e}")
            flash(f'❌ Database error: {str(e)}', 'error')
            return redirect(url_for('history'))
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    return render_template('patient_raw_images.html', patient=patient_info, images=raw_images)

def format_file_size(bytes_size):
    """Format file size in human readable format"""
    if bytes_size >= **********:
        return f"{bytes_size / **********:.2f} GB"
    elif bytes_size >= 1048576:
        return f"{bytes_size / 1048576:.2f} MB"
    elif bytes_size >= 1024:
        return f"{bytes_size / 1024:.2f} KB"
    else:
        return f"{bytes_size} bytes"

@app.route('/patients')
def patients():
    """Display all patients"""
    connection = get_db_connection()
    patients_list = []

    if connection:
        try:
            cursor = connection.cursor(dictionary=True)
            query = """
            SELECT p.*,
                   COUNT(u.id) as upload_count,
                   COUNT(r.id) as analysis_count
            FROM patients p
            LEFT JOIN uploads u ON p.patient_id = u.patient_id
            LEFT JOIN images_result r ON p.patient_id = r.patient_id
            GROUP BY p.patient_id
            ORDER BY p.created_at DESC
            """
            cursor.execute(query)
            patients_list = cursor.fetchall()

        except mysql.connector.Error as e:
            print(f"Database error in patients: {e}")
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    return render_template('patients.html', patients=patients_list)

@app.route('/patient/<patient_id>')
def patient_detail(patient_id):
    """Display detailed patient information"""
    connection = get_db_connection()
    patient = None
    uploads = []
    analyses = []

    if connection:
        try:
            cursor = connection.cursor(dictionary=True)

            # Get patient info
            cursor.execute("SELECT * FROM patients WHERE patient_id = %s", (patient_id,))
            patient = cursor.fetchone()

            # Get uploads
            cursor.execute("SELECT * FROM uploads WHERE patient_id = %s ORDER BY upload_date DESC", (patient_id,))
            uploads = cursor.fetchall()

            # Get analysis results
            cursor.execute("SELECT * FROM images_result WHERE patient_id = %s ORDER BY analysis_date DESC", (patient_id,))
            analyses = cursor.fetchall()

        except mysql.connector.Error as e:
            print(f"Database error in patient detail: {e}")
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    if not patient:
        return "Patient not found", 404

    return render_template('patient_detail.html',
                         patient=patient,
                         uploads=uploads,
                         analyses=analyses)

@app.route('/api/patients')
def api_patients():
    """API endpoint for patient data"""
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'})

    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT * FROM patients ORDER BY created_at DESC")
        patients = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for patient in patients:
            if patient.get('created_at'):
                patient['created_at'] = patient['created_at'].isoformat()
            if patient.get('updated_at'):
                patient['updated_at'] = patient['updated_at'].isoformat()
            if patient.get('date_of_birth'):
                patient['date_of_birth'] = patient['date_of_birth'].isoformat()
            if patient.get('scan_date'):
                patient['scan_date'] = patient['scan_date'].isoformat()

        return jsonify(patients)

    except mysql.connector.Error as e:
        return jsonify({'error': str(e)})
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/uploads')
def api_uploads():
    """API endpoint for uploads data"""
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'})

    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT * FROM uploads ORDER BY scan_date DESC")
        uploads = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for upload in uploads:
            if upload.get('scan_date'):
                upload['scan_date'] = upload['scan_date'].isoformat()

        return jsonify(uploads)

    except mysql.connector.Error as e:
        return jsonify({'error': str(e)})
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/api/images_result')
def api_images_result():
    """API endpoint for images_result data"""
    connection = get_db_connection()
    if not connection:
        return jsonify({'error': 'Database connection failed'})

    try:
        cursor = connection.cursor(dictionary=True)
        cursor.execute("SELECT * FROM images_result ORDER BY scan_date DESC")
        images_result = cursor.fetchall()

        # Convert datetime objects to strings for JSON serialization
        for result in images_result:
            if result.get('scan_date'):
                result['scan_date'] = result['scan_date'].isoformat()
            # Convert decimal confidence to float for JSON serialization
            if result.get('confidence'):
                result['confidence'] = float(result['confidence'])
            if result.get('processing_time'):
                result['processing_time'] = float(result['processing_time'])

        return jsonify(images_result)

    except mysql.connector.Error as e:
        return jsonify({'error': str(e)})
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

# Compatibility route for Upload.php (for existing HTML forms)
@app.route('/Upload.php', methods=['POST'])
def upload_php_compatibility():
    """Handle uploads from existing PHP-style forms"""
    return handle_patient_upload()

@app.route('/test_upload')
def test_upload_page():
    """Serve test upload page"""
    try:
        with open('test_flask_upload.html', 'r') as f:
            return f.read()
    except FileNotFoundError:
        return "Test upload page not found", 404

@app.route('/test_yolo_flask')
def test_yolo_flask():
    """Test YOLO integration in Flask"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Test YOLO function with dummy data
        test_patient_id = f"FLASK_TEST_{int(time.time())}"
        test_patient_name = "Flask Test Patient"

        # Create a simple test image
        import numpy as np
        test_image = np.zeros((200, 200, 3), dtype=np.uint8)
        test_image[50:150, 50:150] = [255, 255, 255]  # White square

        test_image_path = os.path.join(UPLOAD_FOLDER, 'flask_test_image.jpg')
        cv2.imwrite(test_image_path, test_image)

        # Process with YOLO
        result = process_image_with_yolo(test_image_path, 'flask_test_image.jpg', test_patient_id, test_patient_name)

        # Clean up test image
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

        html = f"""
        <h2>🧠 Flask YOLO Integration Test</h2>

        <h3>📊 Test Results:</h3>
        """

        if result:
            html += f"""
            <div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                <h4>✅ YOLO Analysis Successful!</h4>
                <ul>
                    <li><strong>Patient ID:</strong> {test_patient_id}</li>
                    <li><strong>Predicted Class:</strong> {result['predicted_class']}</li>
                    <li><strong>Confidence:</strong> {result['confidence']:.4f} ({result['confidence']*100:.2f}%)</li>
                    <li><strong>Processing Time:</strong> {result['processing_time']}s</li>
                    <li><strong>Mask Area:</strong> {result['mask_area']} pixels</li>
                    <li><strong>Bounding Box:</strong> {result['width']}×{result['height']}</li>
                </ul>
            </div>
            """

            # Check database
            cursor.execute("SELECT * FROM images_result WHERE patient_id = ? ORDER BY analysis_date DESC LIMIT 1", (test_patient_id,))
            db_result = cursor.fetchone()

            if db_result:
                html += f"""
                <div style='background: #e6f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>
                    <h4>✅ Database Storage Successful!</h4>
                    <p>Analysis result saved to 'images_result' table:</p>
                    <ul>
                        <li><strong>Database ID:</strong> {db_result['id']}</li>
                        <li><strong>Analysis Date:</strong> {db_result['analysis_date']}</li>
                        <li><strong>Result Image:</strong> {db_result['result_image']}</li>
                    </ul>
                </div>
                """
            else:
                html += "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'><h4>❌ Database storage failed</h4></div>"
        else:
            html += "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px;'><h4>❌ YOLO analysis failed</h4></div>"

        # Show recent results
        cursor.execute("SELECT * FROM images_result ORDER BY analysis_date DESC LIMIT 5")
        recent_results = cursor.fetchall()

        html += f"""
        <h3>📋 Recent Analysis Results ({len(recent_results)} records):</h3>
        """

        if recent_results:
            html += """
            <table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>
                <tr style='background: #f0f0f0;'>
                    <th>Patient ID</th><th>Image</th><th>Prediction</th><th>Confidence</th><th>Date</th>
                </tr>
            """

            for row in recent_results:
                confidence = row['confidence'] * 100
                predicted_class = row['predicted_class']
                row_color = '#ffe6e6' if 'tumor' in predicted_class.lower() and 'no' not in predicted_class.lower() else '#e6ffe6'

                html += f"""
                <tr style='background: {row_color};'>
                    <td>{row['patient_id']}</td>
                    <td>{row['original_image']}</td>
                    <td><strong>{row['predicted_class'].replace('_', ' ').title()}</strong></td>
                    <td>{confidence:.1f}%</td>
                    <td>{row['analysis_date']}</td>
                </tr>
                """

            html += "</table>"
        else:
            html += "<p>No analysis results found.</p>"

        html += """
        <h3>🔗 Navigation:</h3>
        <p>
            <a href="/test_upload" style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Test Upload</a>
            <a href="/check_results" style='background: #17a2b8; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>📊 View Results</a>
            <a href="/test_db" style='background: #6c757d; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px;'>🔍 Database Status</a>
        </p>
        """

        return html

    except Exception as e:
        return f"❌ Error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/check_uploads')
def check_uploads():
    """Check what's in the uploads table"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get all uploads
        cursor.execute("SELECT * FROM uploads ORDER BY upload_date DESC LIMIT 20")
        uploads = cursor.fetchall()

        # Get upload count
        cursor.execute("SELECT COUNT(*) as total FROM uploads")
        total_count = cursor.fetchone()['total']

        html = f"""
        <h2>📁 "uploads" Table Contents</h2>
        <p><strong>✅ Total files in "uploads" table:</strong> {total_count}</p>
        <p><em>This table stores all uploaded image files with patient associations.</em></p>

        <h3>Recent Uploads:</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background: #f0f0f0;">
                <th>ID</th>
                <th>Patient ID</th>
                <th>File Name</th>
                <th>File Path</th>
                <th>File Size</th>
                <th>File Type</th>
                <th>Upload Date</th>
            </tr>
        """

        for upload in uploads:
            html += f"""
            <tr>
                <td>{upload['id']}</td>
                <td>{upload['patient_id']}</td>
                <td>{upload['file_name']}</td>
                <td>{upload['file_path']}</td>
                <td>{format_file_size(upload['file_size'])}</td>
                <td>{upload['file_type']}</td>
                <td>{upload['upload_date']}</td>
            </tr>
            """

        html += """
        </table>
        <br>
        <h3>📊 Quick Stats:</h3>
        <ul>
            <li><strong>Patient Info:</strong> Stored in "patients" table</li>
            <li><strong>Uploaded Files:</strong> Stored in "uploads" table</li>
            <li><strong>Analysis Results:</strong> Stored in "images_result" table</li>
        </ul>
        <p><a href="/test_upload">🧪 Test Upload</a> | <a href="/test_db">🔍 Database Status</a> | <a href="/check_patients">👥 Check Patients</a></p>
        """

        return html

    except mysql.connector.Error as e:
        return f"❌ Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/check_patients')
def check_patients():
    """Check what's in the patients table"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get all patients
        cursor.execute("SELECT * FROM patients ORDER BY created_at DESC LIMIT 20")
        patients = cursor.fetchall()

        # Get patient count
        cursor.execute("SELECT COUNT(*) as total FROM patients")
        total_count = cursor.fetchone()['total']

        html = f"""
        <h2>👥 "patients" Table Contents</h2>
        <p><strong>✅ Total patients in "patients" table:</strong> {total_count}</p>
        <p><em>This table stores all patient information.</em></p>

        <h3>Recent Patients:</h3>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background: #f0f0f0;">
                <th>ID</th>
                <th>Patient ID</th>
                <th>Patient Name</th>
                <th>Gender</th>
                <th>Age</th>
                <th>Date of Birth</th>
                <th>Radiologist</th>
                <th>Created At</th>
            </tr>
        """

        for patient in patients:
            html += f"""
            <tr>
                <td>{patient['id']}</td>
                <td><strong>{patient['patient_id']}</strong></td>
                <td>{patient['patient_name']}</td>
                <td>{patient['gender']}</td>
                <td>{patient['age']}</td>
                <td>{patient['date_of_birth']}</td>
                <td>{patient['radiologist']}</td>
                <td>{patient['created_at']}</td>
            </tr>
            """

        html += """
        </table>
        <br>
        <p><a href="/check_uploads">📁 Check Uploads</a> | <a href="/test_upload">🧪 Test Upload</a> | <a href="/test_db">🔍 Database Status</a></p>
        """

        return html

    except mysql.connector.Error as e:
        return f"❌ Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()


@app.route('/check_results')
def check_results():
    """Check what's in the images_result table"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get all analysis results
        cursor.execute("SELECT * FROM images_result ORDER BY analysis_date DESC LIMIT 20")
        results = cursor.fetchall()

        # Get results count
        cursor.execute("SELECT COUNT(*) as total FROM images_result")
        total_count = cursor.fetchone()['total']

        html = f"""
        <h2>🧠 "images_result" Table Contents</h2>
        <p><strong>✅ Total analysis results in "images_result" table:</strong> {total_count}</p>
        <p><em>This table stores YOLO model analysis results for processed images.</em></p>

        <h3>Recent Analysis Results:</h3>
        <table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">
            <tr style="background: #f0f0f0;">
                <th>ID</th>
                <th>Patient ID</th>
                <th>Patient Name</th>
                <th>Result Image</th>
                <th>Predicted Class</th>
                <th>Confidence</th>
                <th>Bounding Box (x1,y1,x2,y2)</th>
                <th>Dimensions (W×H)</th>
                <th>Mask Area</th>

                <th>Analysis Date</th>
            </tr>
        """

        for result in results:
            confidence_percent = f"{float(result['confidence']) * 100:.2f}%"
            bbox = f"({result['x1']},{result['y1']},{result['x2']},{result['y2']})"
            dimensions = f"{result['width']}×{result['height']}"


            # Color code by prediction
            row_color = "#e8f5e8" if "no tumor" in result['predicted_class'].lower() else "#ffe8e8"

            html += f"""
            <tr style="background: {row_color};">
                <td>{result['id']}</td>
                <td><strong>{result['patient_id']}</strong></td>
                <td>{result['patient_name']}</td>
                <td>{result['result_image']}</td>
                <td><strong>{result['predicted_class'].replace('_', ' ').title()}</strong></td>
                <td>{confidence_percent}</td>
                <td>{bbox}</td>
                <td>{dimensions}</td>
                <td>{result['mask_area']}</td>

                <td>{result['analysis_date']}</td>
            </tr>
            """

        html += """
        </table>
        <br>
        <h3>📊 Analysis Summary:</h3>
        <ul>
            <li><strong>Green rows:</strong> No tumor detected</li>
            <li><strong>Red rows:</strong> Tumor detected</li>
            <li><strong>Confidence:</strong> Model prediction confidence</li>
            <li><strong>Bounding Box:</strong> Coordinates of detected region</li>
            <li><strong>Mask Area:</strong> Size of detected region in pixels</li>
        </ul>
        <p><a href="/check_patients">👥 Check Patients</a> | <a href="/check_uploads">📁 Check Uploads</a> | <a href="/test_upload">🧪 Test Upload</a></p>
        """

        return html

    except mysql.connector.Error as e:
        return f"❌ Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()


@app.route('/analysis_summary')
def analysis_summary():
    """Display analysis summary with visual results"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get recent analysis results
        query = """
        SELECT r.*
        FROM images_result r
        ORDER BY r.analysis_date DESC
        LIMIT 10
        """
        cursor.execute(query)
        results = cursor.fetchall()

        # Get summary statistics
        cursor.execute("SELECT predicted_class, COUNT(*) as count FROM images_result GROUP BY predicted_class")
        stats = cursor.fetchall()

        html = """
        <h2>🧠 YOLO Analysis Summary</h2>
        <style>
            .analysis-card { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 8px; }
            .tumor-detected { background: #ffe6e6; border-color: #ff9999; }
            .no-tumor { background: #e6ffe6; border-color: #99ff99; }
            .confidence-high { color: #006600; font-weight: bold; }
            .confidence-medium { color: #cc6600; font-weight: bold; }
            .confidence-low { color: #cc0000; font-weight: bold; }
        </style>

        <h3>📊 Analysis Statistics:</h3>
        <ul>
        """

        for stat in stats:
            html += f"<li><strong>{stat['predicted_class'].replace('_', ' ').title()}:</strong> {stat['count']} cases</li>"

        html += """
        </ul>

        <h3>🔬 Recent Analysis Results:</h3>
        """

        for result in results:
            confidence_percent = float(result['confidence']) * 100
            confidence_class = 'confidence-high' if confidence_percent >= 80 else 'confidence-medium' if confidence_percent >= 60 else 'confidence-low'
            card_class = 'tumor-detected' if 'tumor' in result['predicted_class'].lower() and 'no' not in result['predicted_class'].lower() else 'no-tumor'

            html += f"""
            <div class="analysis-card {card_class}">
                <h4>Patient: {result['patient_name']} ({result['patient_id']})</h4>
                <p><strong>Result:</strong> <span class="{confidence_class}">{result['predicted_class'].replace('_', ' ').title()}</span></p>
                <p><strong>Confidence:</strong> <span class="{confidence_class}">{confidence_percent:.2f}%</span></p>
                <p><strong>Analysis Date:</strong> {result['analysis_date']}</p>
                <p><strong>Processing Time:</strong> {result['processing_time']}s</p>
                {f"<p><strong>Detection Area:</strong> {result['mask_area']} pixels ({result['width']}×{result['height']})</p>" if result['mask_area'] > 0 else ""}

            </div>
            """

        html += """
        <br>
        <p><a href="/check_results">📋 View Full Results Table</a> | <a href="/test_upload">🧪 Test Upload</a> | <a href="/test_db">🔍 Database Status</a></p>
        """

        return html

    except mysql.connector.Error as e:
        return f"❌ Database error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/quick_fix')
def quick_fix():
    """Quick diagnostic and fix for image display issues"""
    html = """
    <h2>🔧 Quick Image Display Fix</h2>
    <p>This page helps diagnose and fix image display issues.</p>

    <h3>🔍 Diagnostic Links:</h3>
    <ul>
        <li><a href="/debug/report/197">🔍 Debug Report ID 197 (from your screenshot)</a></li>
        <li><a href="/test_db">📊 Check Database Status</a></li>
        <li><a href="/check_results">📋 View All Results</a></li>
        <li><a href="/debug/external_patients">📁 Check External Patient Directory</a></li>
        <li><a href="/debug/processed_images">🖼️ Debug Processed Images Issue</a></li>
        <li><a href="/test_patient_image">🖼️ Test Patient Image Serving</a></li>
    </ul>

    <h3>🚀 Quick Actions:</h3>
    <ul>
        <li><a href="/guarantee_image_display">🛡️ GUARANTEE All Images Display</a></li>
        <li><a href="/test_complete_workflow">🧪 Test Complete Workflow</a></li>
        <li><a href="/report/197">📊 Try Original Report (ID 197)</a></li>
        <li><a href="/report">📊 Report Dashboard</a></li>
        <li><a href="/upload">📤 Upload New File</a></li>
    </ul>

    <h3>📝 Instructions:</h3>
    <ol>
        <li>Click "Debug Report ID 197" to see exactly what's wrong with the image</li>
        <li>Check if the image file exists in the expected locations</li>
        <li>If the image is missing, you may need to re-upload the patient file</li>
    </ol>
    """
    return html

@app.route('/debug/processed_images')
def debug_processed_images():
    """Debug processed/result images issue"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get recent results to check
        cursor.execute("""
            SELECT id, patient_id, result_image, predicted_class, confidence, scan_date
            FROM images_result
            ORDER BY scan_date DESC
            LIMIT 10
        """)
        results = cursor.fetchall()

        html = """
        <h2>🔍 Debug Processed Images Issue</h2>
        <p>Checking where processed result images should be and if they exist.</p>
        """

        if not results:
            html += "<p>❌ No results found in database</p>"
            return html

        html += f"<h3>📊 Found {len(results)} recent results:</h3>"

        for result in results:
            patient_id = result['patient_id']
            result_image = result['result_image']
            result_id = result['id']

            html += f"<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>"
            html += f"<h4>🔍 Result ID: {result_id} | Patient: {patient_id}</h4>"
            html += f"<p><strong>Expected Image:</strong> {result_image}</p>"
            html += f"<p><strong>Predicted Class:</strong> {result['predicted_class'].replace('_', ' ').title()}</p>"
            html += f"<p><strong>Confidence:</strong> {result['confidence']}</p>"

            # Check where the processed image should be
            expected_paths = [
                os.path.join('static', 'results', f'result_{patient_id}', result_image),
                os.path.join('static', 'results', result_image),
                os.path.join('static', 'results', 'result_UNKNOWN', result_image),
            ]

            html += "<p><strong>Checking processed image locations:</strong></p><ul>"
            found_image = False
            for path in expected_paths:
                exists = os.path.exists(path)
                html += f"<li>{path}: {'✅ EXISTS' if exists else '❌ NOT FOUND'}</li>"
                if exists and not found_image:
                    found_image = True
                    html += f"<p>✅ <strong>Image found! Testing display:</strong></p>"
                    html += f'<img src="/patient_image/{patient_id}/{result_image}" style="max-width: 200px; border: 1px solid #ccc;" alt="Processed Image">'
            html += "</ul>"

            if not found_image:
                html += "<p>❌ <strong>Processed image not found in any location!</strong></p>"

                # Check if raw image exists to potentially reprocess
                raw_image_path = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
                if os.path.exists(raw_image_path):
                    raw_files = os.listdir(raw_image_path)
                    html += f"<p>📁 Raw images available in {raw_image_path}: {len(raw_files)} files</p>"
                    html += f"<p>💡 <strong>Solution:</strong> The processed image needs to be regenerated from the raw image.</p>"
                else:
                    html += f"<p>❌ Raw image directory also not found: {raw_image_path}</p>"

            html += f"<p><a href='/debug/report/{result_id}'>🔍 Debug this specific result</a></p>"
            html += "</div>"

        # Add summary and solution
        html += """
        <h3>💡 Common Solutions:</h3>
        <ul>
            <li><strong>Missing processed images:</strong> Run YOLO processing on uploaded images to generate result images with bounding boxes</li>
            <li><strong>Wrong file paths:</strong> Check if images are in the correct static/results/result_{patient_id}/ folders</li>
            <li><strong>Database mismatch:</strong> Ensure result_image field matches actual filenames</li>
        </ul>

        <h3>🔗 Quick Actions:</h3>
        <ul>
            <li><a href="/regenerate_missing_images">🔄 Regenerate Missing Processed Images</a></li>
            <li><a href="/check_results">📋 View All Database Results</a></li>
        </ul>
        """

        return html

    except Exception as e:
        return f"❌ Error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/test_patient_image')
def test_patient_image():
    """Test patient image serving with known files"""
    html = """
    <h2>🖼️ Test Patient Image Serving</h2>
    <p>Testing image serving from the external patient directory.</p>
    """

    # Test with known patient files
    test_cases = [
        ('P0000', 'Tr-gl_0000_jpg.rf.16ada59e6c7d277d517310959a7658db.jpg'),
        ('P0007', 'Tr-gl_0012_jpg.rf.e3e700cbcd9639dd2a9cf7db1b05abcb.jpg'),
    ]

    for patient_id, filename in test_cases:
        patient_path = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
        file_path = os.path.join(patient_path, filename)

        html += f"<h3>📁 Testing {patient_id}</h3>"
        html += f"<p><strong>Patient Directory:</strong> {patient_path}</p>"
        html += f"<p><strong>Directory Exists:</strong> {os.path.exists(patient_path)}</p>"
        html += f"<p><strong>File Path:</strong> {file_path}</p>"
        html += f"<p><strong>File Exists:</strong> {os.path.exists(file_path)}</p>"

        if os.path.exists(file_path):
            html += f"<p><strong>✅ Image should display below:</strong></p>"
            html += f'<img src="/patient_image/{patient_id}/{filename}" style="max-width: 300px; border: 1px solid #ccc; margin: 10px;" alt="{patient_id} Image">'
        else:
            html += f"<p><strong>❌ File not found</strong></p>"

    return html

@app.route('/regenerate_missing_images')
def regenerate_missing_images():
    """Regenerate missing processed images from raw patient images"""
    connection = get_db_connection()
    if not connection:
        return "❌ Database connection failed"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get uploads for P0004-P0008 to create missing processed images
        cursor.execute("""
            SELECT u.patient_id, u.file_name, u.scan_date
            FROM uploads u
            WHERE u.patient_id IN ('P0004', 'P0005', 'P0006', 'P0007', 'P0008')
            AND NOT EXISTS (
                SELECT 1 FROM images_result r
                WHERE r.patient_id = u.patient_id
                AND r.original_image = u.file_name
            )
            ORDER BY u.patient_id, u.scan_date DESC
        """)
        results = cursor.fetchall()

        html = """
        <h2>🔄 Regenerate Missing Processed Images</h2>
        <p>Checking and regenerating missing processed result images...</p>
        """

        regenerated = 0
        already_exist = 0
        failed = 0

        for result in results:
            patient_id = result['patient_id']
            result_image = result['result_image']
            original_image = result.get('original_image', '')

            # Check if processed image exists
            processed_path = os.path.join('static', 'results', f'result_{patient_id}', result_image)

            if os.path.exists(processed_path):
                already_exist += 1
                continue

            html += f"<p>🔄 Processing {patient_id}: {result_image}</p>"

            # Try to find the original image
            possible_original_paths = []

            if original_image:
                # Try with the stored original image name
                possible_original_paths.extend([
                    os.path.join(EXTERNAL_PATIENT_DIR, patient_id, original_image),
                    os.path.join('static', 'uploads', patient_id, original_image),
                    os.path.join('static', 'uploads', original_image),
                ])

            # Try to match with available files in patient directory
            patient_dir = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
            if os.path.exists(patient_dir):
                patient_files = os.listdir(patient_dir)
                # Look for files that might match the result image name
                base_name = result_image.replace('result_', '').replace('.jpg', '')
                for file in patient_files:
                    if base_name in file or file.replace('.jpg', '') in base_name:
                        possible_original_paths.append(os.path.join(patient_dir, file))

            # Try to regenerate from the first available original image
            original_found = None
            for orig_path in possible_original_paths:
                if os.path.exists(orig_path):
                    original_found = orig_path
                    break

            if original_found:
                try:
                    # Create patient result directory
                    result_dir = os.path.join('static', 'results', f'result_{patient_id}')
                    os.makedirs(result_dir, exist_ok=True)

                    # Process the image with YOLO
                    import cv2
                    import numpy as np
                    image = cv2.imread(original_found)
                    if image is not None:
                        # Run YOLO model (simplified version)
                        results_yolo = model(original_found)[0]

                        if results_yolo.boxes:
                            # Draw bounding box
                            top = results_yolo.boxes[0]
                            x1, y1, x2, y2 = map(int, top.xyxy[0])
                            confidence = float(top.conf[0])
                            predicted_class = model.names[int(top.cls[0])]

                            # Process segmentation masks if available
                            if hasattr(results_yolo, 'masks') and results_yolo.masks is not None:
                                # Get the mask for the first detection
                                mask = results_yolo.masks.data[0].cpu().numpy()
                                mask = (mask * 255).astype(np.uint8)

                                # Resize mask to match image dimensions if needed
                                if mask.shape != image.shape[:2]:
                                    mask = cv2.resize(mask, (image.shape[1], image.shape[0]))

                                # Use enhanced visualization
                                image = create_enhanced_mask_visualization(
                                    image, mask, predicted_class, confidence, (x1, y1, x2, y2)
                                )
                            else:
                                # Draw simple bounding box for non-segmentation models
                                label = f"{predicted_class} {confidence:.2f}"
                                cv2.rectangle(image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                                cv2.putText(image, label, (x1, y1 - 10),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

                        # Save the processed image
                        success = cv2.imwrite(processed_path, image)
                        if success:
                            html += f"  ✅ Regenerated: {processed_path}<br>"
                            regenerated += 1
                        else:
                            html += f"  ❌ Failed to save: {processed_path}<br>"
                            failed += 1
                    else:
                        html += f"  ❌ Could not load image: {original_found}<br>"
                        failed += 1

                except Exception as e:
                    html += f"  ❌ Error processing {original_found}: {str(e)}<br>"
                    failed += 1
            else:
                html += f"  ❌ No original image found for {patient_id}<br>"
                failed += 1

        html += f"""
        <h3>📊 Summary:</h3>
        <ul>
            <li>✅ Already existed: {already_exist}</li>
            <li>🔄 Regenerated: {regenerated}</li>
            <li>❌ Failed: {failed}</li>
        </ul>

        <p><a href="/debug/processed_images">🔍 Check Processed Images Again</a></p>
        <p><a href="/report/197">📊 Test Original Report</a></p>
        """

        return html

    except Exception as e:
        return f"❌ Error: {str(e)}"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/test_complete_workflow')
def test_complete_workflow():
    """Test the complete upload -> process -> display workflow"""
    html = """
    <h2>🧪 Complete Workflow Test</h2>
    <p>Testing the entire patient file processing workflow to ensure images always display.</p>
    """

    connection = get_db_connection()
    if not connection:
        return html + "<p>❌ Database connection failed</p>"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get the most recent 5 results to test
        cursor.execute("""
            SELECT id, patient_id, result_image, predicted_class, confidence, scan_date
            FROM images_result
            ORDER BY scan_date DESC
            LIMIT 5
        """)
        results = cursor.fetchall()

        html += f"<h3>📊 Testing {len(results)} Recent Results:</h3>"

        all_working = True

        for result in results:
            patient_id = result['patient_id']
            result_image = result['result_image']
            result_id = result['id']

            html += f"<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>"
            html += f"<h4>🔍 Testing Result ID: {result_id} | Patient: {patient_id}</h4>"

            # Test 1: Check if processed image exists
            image_exists, verified_path = verify_processed_image_exists(patient_id, result_image)
            if image_exists:
                html += f"<p>✅ <strong>Test 1 PASSED:</strong> Processed image exists at {verified_path}</p>"

                # Test 2: Check if image can be served via URL
                html += f"<p>✅ <strong>Test 2:</strong> Image serving test:</p>"
                html += f'<img src="/patient_image/{patient_id}/{result_image}" style="max-width: 200px; border: 1px solid #ccc;" alt="Test Image">'

                # Test 3: Check report accessibility
                html += f"<p>✅ <strong>Test 3:</strong> <a href='/report/{result_id}' target='_blank'>View Full Report</a></p>"

            else:
                html += f"<p>❌ <strong>Test 1 FAILED:</strong> Processed image missing: {result_image}</p>"
                html += f"<p>💡 <strong>Solution:</strong> <a href='/guarantee_image_display'>Fix All Images</a></p>"
                all_working = False

            html += "</div>"

        # Overall status
        if all_working:
            html += """
            <div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>
                <h3>✅ WORKFLOW TEST PASSED</h3>
                <p>All processed images are properly generated and accessible. Patient reports should display images correctly.</p>
            </div>
            """
        else:
            html += """
            <div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>
                <h3>❌ WORKFLOW TEST FAILED</h3>
                <p>Some processed images are missing. Use the fix tool to resolve this issue.</p>
            </div>
            """

        return html

    except Exception as e:
        return html + f"<p>❌ Error: {str(e)}</p>"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

@app.route('/guarantee_image_display')
def guarantee_image_display():
    """Guarantee that all patient reports will display processed images"""
    html = """
    <h2>🛡️ Guarantee Image Display for All Patients</h2>
    <p>This function ensures ALL patient reports will display processed images correctly.</p>
    """

    connection = get_db_connection()
    if not connection:
        return html + "<p>❌ Database connection failed</p>"

    try:
        cursor = connection.cursor(dictionary=True)

        # Get all results that might have missing images
        cursor.execute("""
            SELECT id, patient_id, result_image, original_image
            FROM images_result
            ORDER BY scan_date DESC
            LIMIT 50
        """)
        results = cursor.fetchall()

        html += f"<h3>🔍 Checking {len(results)} Recent Results:</h3>"

        fixed_count = 0
        already_working = 0
        failed_count = 0

        for result in results:
            patient_id = result['patient_id']
            result_image = result['result_image']

            # Check if processed image exists
            image_exists, verified_path = verify_processed_image_exists(patient_id, result_image)

            if image_exists:
                already_working += 1
                continue

            html += f"<p>🔄 Fixing missing image for {patient_id}: {result_image}</p>"

            # Try to find original image in patient directory
            patient_dir = os.path.join(EXTERNAL_PATIENT_DIR, patient_id)
            if os.path.exists(patient_dir):
                patient_files = [f for f in os.listdir(patient_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                if patient_files:
                    original_path = os.path.join(patient_dir, patient_files[0])
                    try:
                        # Reprocess the image
                        reprocess_result = process_image_with_yolo(
                            original_path,
                            patient_files[0],
                            patient_id,
                            f"Patient {patient_id}"
                        )

                        if reprocess_result:
                            html += f"  ✅ Successfully regenerated<br>"
                            fixed_count += 1
                        else:
                            html += f"  ❌ Failed to regenerate<br>"
                            failed_count += 1

                    except Exception as e:
                        html += f"  ❌ Error: {str(e)}<br>"
                        failed_count += 1
                else:
                    html += f"  ❌ No images found in {patient_dir}<br>"
                    failed_count += 1
            else:
                html += f"  ❌ Patient directory not found: {patient_dir}<br>"
                failed_count += 1

        # Summary
        html += f"""
        <div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 20px 0; border-radius: 5px;'>
            <h3>📊 Summary:</h3>
            <ul>
                <li>✅ Already working: {already_working}</li>
                <li>🔄 Fixed: {fixed_count}</li>
                <li>❌ Failed: {failed_count}</li>
                <li><strong>Total processed: {len(results)}</strong></li>
            </ul>
        </div>

        <h3>🎯 Result:</h3>
        <p><strong>{already_working + fixed_count} out of {len(results)} patient reports now have guaranteed image display!</strong></p>

        <p><a href="/test_complete_workflow">🧪 Test Complete Workflow</a></p>
        """

        return html

    except Exception as e:
        return html + f"<p>❌ Error: {str(e)}</p>"
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

if __name__ == '__main__':
    print("🚀 Starting Radiolens Flask Application...")
    print("📊 Database tables will be created automatically if they don't exist")
    print("🌐 Access the application at: http://127.0.0.1:5000")
    app.run(debug=True, host='127.0.0.1', port=5000)
