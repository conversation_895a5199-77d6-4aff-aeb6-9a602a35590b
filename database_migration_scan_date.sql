-- =====================================================
-- DATABASE MIGRATION: RENAME DATE COLUMNS TO SCAN_DATE
-- =====================================================
-- This script renames date columns in all tables to use 'scan_date'
-- Run this in phpMyAdmin or MySQL command line

USE patient_info;

-- =====================================================
-- 1. PATIENTS TABLE: updated_at -> scan_date
-- =====================================================
-- Check if updated_at column exists and rename it
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'patient_info' 
    AND TABLE_NAME = 'patients' 
    AND COLUMN_NAME = 'updated_at'
);

SET @sql = IF(@column_exists > 0,
    'ALTER TABLE patients CHANGE COLUMN updated_at scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT "patients.updated_at column does not exist, skipping..." as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 2. UPLOADS TABLE: upload_date -> scan_date
-- =====================================================
-- Check if upload_date column exists and rename it
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'patient_info' 
    AND TABLE_NAME = 'uploads' 
    AND COLUMN_NAME = 'upload_date'
);

SET @sql = IF(@column_exists > 0,
    'ALTER TABLE uploads CHANGE COLUMN upload_date scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP',
    'SELECT "uploads.upload_date column does not exist, skipping..." as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. IMAGES_RESULT TABLE: analysis_date -> scan_date
-- =====================================================
-- Check if analysis_date column exists and rename it
SET @column_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_SCHEMA = 'patient_info' 
    AND TABLE_NAME = 'images_result' 
    AND COLUMN_NAME = 'analysis_date'
);

SET @sql = IF(@column_exists > 0,
    'ALTER TABLE images_result CHANGE COLUMN analysis_date scan_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP',
    'SELECT "images_result.analysis_date column does not exist, skipping..." as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. UPDATE INDEX FOR IMAGES_RESULT TABLE
-- =====================================================
-- Drop old index if it exists
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'patient_info' 
    AND TABLE_NAME = 'images_result' 
    AND INDEX_NAME = 'idx_analysis_date'
);

SET @sql = IF(@index_exists > 0,
    'DROP INDEX idx_analysis_date ON images_result',
    'SELECT "idx_analysis_date index does not exist, skipping..." as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create new index for scan_date
SET @index_exists = (
    SELECT COUNT(*) 
    FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE TABLE_SCHEMA = 'patient_info' 
    AND TABLE_NAME = 'images_result' 
    AND INDEX_NAME = 'idx_scan_date'
);

SET @sql = IF(@index_exists = 0,
    'CREATE INDEX idx_scan_date ON images_result (scan_date)',
    'SELECT "idx_scan_date index already exists, skipping..." as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================
-- Check the updated table structures
SELECT 'PATIENTS TABLE STRUCTURE:' as info;
DESCRIBE patients;

SELECT 'UPLOADS TABLE STRUCTURE:' as info;
DESCRIBE uploads;

SELECT 'IMAGES_RESULT TABLE STRUCTURE:' as info;
DESCRIBE images_result;

-- Check indexes on images_result table
SELECT 'IMAGES_RESULT INDEXES:' as info;
SHOW INDEX FROM images_result WHERE Key_name LIKE '%scan_date%';

-- =====================================================
-- MIGRATION COMPLETE!
-- =====================================================
SELECT '✅ DATABASE MIGRATION COMPLETED SUCCESSFULLY!' as status;
SELECT 'All date columns have been renamed to scan_date' as message;
