"""
Dashboard Data Service for RadioLens
Handles data aggregation and processing for dashboard visualizations
"""

import mysql.connector
import pandas as pd
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any, Optional

# Class mapping for predicted_class values
CLASS_MAPPING = {
    'glioma': 'Glioma Tumor',
    'glioma_tumor': 'Glioma Tumor',
    'meningioma': 'Meningioma Tumor',
    'meningioma_tumor': 'Meningioma Tumor',
    'pituitary': 'Pituitary Tumor',
    'pituitary_tumor': 'Pituitary Tumor',
    'no_tumor': 'No Tumor',
    'no_tumour': 'No Tumor',
    'healthy': 'No Tumor'
}

def map_predicted_class(predicted_class):
    """
    Map predicted class values to user-friendly display names

    Args:
        predicted_class (str): Raw predicted class from model or database

    Returns:
        str: User-friendly display name
    """
    if not predicted_class:
        return 'Unknown'

    # Convert to lowercase for case-insensitive matching
    class_lower = predicted_class.lower().strip()

    # Check direct mapping first
    if class_lower in CLASS_MAPPING:
        return CLASS_MAPPING[class_lower]

    # Fallback: basic string formatting for unmapped values
    return predicted_class.replace('_', ' ').title()

class DashboardDataService:
    def __init__(self, db_config: Dict[str, Any]):
        """Initialize the dashboard data service with database configuration"""
        self.db_config = db_config
        
    def get_db_connection(self):
        """Get database connection"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            return connection
        except mysql.connector.Error as e:
            print(f"Database connection error: {e}")
            return None
    
    def get_overview_stats(self) -> Dict[str, Any]:
        """Get overview statistics for the dashboard"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            # Total patients
            cursor.execute("SELECT COUNT(DISTINCT patient_id) as total_patients FROM patients")
            total_patients = cursor.fetchone()['total_patients'] or 0
            
            # Total images uploaded
            cursor.execute("SELECT COUNT(*) as total_uploads FROM uploads")
            total_uploads = cursor.fetchone()['total_uploads'] or 0
            
            # Total images processed (analyzed)
            cursor.execute("SELECT COUNT(*) as total_processed FROM images_result")
            total_processed = cursor.fetchone()['total_processed'] or 0
            
            return {
                'total_patients': total_patients,
                'total_uploads': total_uploads,
                'total_processed': total_processed
            }
            
        except mysql.connector.Error as e:
            print(f"Error getting overview stats: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_gender_distribution(self) -> Dict[str, Any]:
        """Get gender distribution data"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT p.gender, COUNT(DISTINCT p.patient_id) as count
            FROM patients p
            LEFT JOIN uploads u ON p.patient_id = u.patient_id
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE p.gender IS NOT NULL
                AND (u.patient_id IS NOT NULL OR ir.patient_id IS NOT NULL)
            GROUP BY p.gender
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Convert to format suitable for charts
            data = {}
            total = sum(row['count'] for row in results)
            
            for row in results:
                gender = row['gender'].capitalize()
                count = row['count']
                percentage = (count / total * 100) if total > 0 else 0
                data[gender] = {
                    'count': count,
                    'percentage': round(percentage, 1)
                }
            
            return data
            
        except mysql.connector.Error as e:
            print(f"Error getting gender distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_tumor_types_distribution(self) -> Dict[str, Any]:
        """Get tumor types distribution from analysis results"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT ir.predicted_class, COUNT(*) as count
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.predicted_class IS NOT NULL
            GROUP BY ir.predicted_class
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Convert to format suitable for charts
            data = {}
            total = sum(row['count'] for row in results)

            for row in results:
                tumor_type = map_predicted_class(row['predicted_class'])
                count = row['count']
                percentage = (count / total * 100) if total > 0 else 0
                data[tumor_type] = {
                    'count': count,
                    'percentage': round(percentage, 1)
                }
            
            return data
            
        except mysql.connector.Error as e:
            print(f"Error getting tumor types distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_confidence_score_distribution(self) -> Dict[str, Any]:
        """Get confidence score distribution for heatmap"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT ir.predicted_class, ir.confidence
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.confidence > 0
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Group by predicted class and confidence ranges
            confidence_data = {}
            
            for row in results:
                tumor_type = row['predicted_class']
                confidence = float(row['confidence'])
                
                if tumor_type not in confidence_data:
                    confidence_data[tumor_type] = []
                
                confidence_data[tumor_type].append(confidence)
            
            return confidence_data
            
        except mysql.connector.Error as e:
            print(f"Error getting confidence distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_age_distribution(self) -> Dict[str, Any]:
        """Get age distribution data"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT p.age, COUNT(DISTINCT p.patient_id) as count
            FROM patients p
            LEFT JOIN uploads u ON p.patient_id = u.patient_id
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE p.age IS NOT NULL
                AND (u.patient_id IS NOT NULL OR ir.patient_id IS NOT NULL)
            GROUP BY p.age
            ORDER BY p.age
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Group ages into ranges for better visualization
            age_ranges = {
                '0-20': 0, '21-40': 0, '41-60': 0, '61-80': 0, '80+': 0
            }
            
            for row in results:
                age = row['age']
                count = row['count']
                
                if age <= 20:
                    age_ranges['0-20'] += count
                elif age <= 40:
                    age_ranges['21-40'] += count
                elif age <= 60:
                    age_ranges['41-60'] += count
                elif age <= 80:
                    age_ranges['61-80'] += count
                else:
                    age_ranges['80+'] += count
            
            return age_ranges
            
        except mysql.connector.Error as e:
            print(f"Error getting age distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_brain_tumor_cases_timeline(self) -> Dict[str, Any]:
        """Get brain tumor cases over time for line chart"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        
        try:
            cursor = connection.cursor(dictionary=True)
            
            # Get cases by month for the last 12 months
            query = """
            SELECT
                DATE_FORMAT(ir.scan_date, '%Y-%m') as month,
                COUNT(*) as cases
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.predicted_class NOT IN ('no_tumor', 'no_tumour', 'healthy')
                AND ir.scan_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(ir.scan_date, '%Y-%m')
            ORDER BY month
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            # Convert to format suitable for line chart
            months = []
            cases = []
            
            for row in results:
                month_str = row['month']
                # Convert to readable format (e.g., "Jul", "Aug")
                month_obj = datetime.strptime(month_str, '%Y-%m')
                month_name = month_obj.strftime('%b')
                
                months.append(month_name)
                cases.append(row['cases'])
            
            return {
                'months': months,
                'cases': cases
            }
            
        except mysql.connector.Error as e:
            print(f"Error getting brain tumor timeline: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
    
    def get_all_dashboard_data(self) -> Dict[str, Any]:
        """Get all dashboard data in one call"""
        return {
            'overview_stats': self.get_overview_stats(),
            'gender_distribution': self.get_gender_distribution(),
            'tumor_types_distribution': self.get_tumor_types_distribution(),
            'confidence_distribution': self.get_confidence_score_distribution(),
            'age_distribution': self.get_age_distribution(),
            'tumor_timeline': self.get_brain_tumor_cases_timeline(),
            'last_updated': datetime.now().isoformat()
        }

    def get_filtered_dashboard_data(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, Any]:
        """Get filtered dashboard data based on provided filters"""
        return {
            'overview_stats': self.get_filtered_overview_stats(gender, tumor_type, age_range),
            'gender_distribution': self.get_filtered_gender_distribution(gender, tumor_type, age_range),
            'tumor_types_distribution': self.get_filtered_tumor_types_distribution(gender, tumor_type, age_range),
            'confidence_distribution': self.get_filtered_confidence_score_distribution(gender, tumor_type, age_range),
            'age_distribution': self.get_filtered_age_distribution(gender, tumor_type, age_range),
            'tumor_timeline': self.get_filtered_brain_tumor_cases_timeline(gender, tumor_type, age_range),
            'last_updated': datetime.now().isoformat()
        }

    def _build_filter_conditions(self, gender=None, tumor_type=None, age_range=None):
        """Build WHERE conditions for filtering"""
        conditions = []
        params = []

        if gender:
            conditions.append("p.gender = %s")
            params.append(gender)

        if tumor_type and tumor_type != 'All':
            # Map the display name back to possible database values
            reverse_mapping = {}
            for db_val, display_val in CLASS_MAPPING.items():
                if display_val not in reverse_mapping:
                    reverse_mapping[display_val] = []
                reverse_mapping[display_val].append(db_val)

            if tumor_type in reverse_mapping:
                placeholders = ', '.join(['%s'] * len(reverse_mapping[tumor_type]))
                conditions.append(f"ir.predicted_class IN ({placeholders})")
                params.extend(reverse_mapping[tumor_type])
            else:
                conditions.append("ir.predicted_class = %s")
                params.append(tumor_type)

        if age_range:
            # Parse age range like "20-30"
            if '-' in age_range:
                min_age, max_age = age_range.split('-')
                conditions.append("p.age BETWEEN %s AND %s")
                params.extend([int(min_age), int(max_age)])

        where_clause = " AND " + " AND ".join(conditions) if conditions else ""
        return where_clause, params

    def get_filtered_overview_stats(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, int]:
        """Get filtered overview statistics"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            # Total patients
            query = f"""
            SELECT COUNT(DISTINCT p.patient_id) as total_patients
            FROM patients p
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE 1=1{where_clause}
            """
            cursor.execute(query, params)
            total_patients = cursor.fetchone()['total_patients']

            # Total uploads and processed
            query = f"""
            SELECT
                COUNT(*) as total_uploads,
                COUNT(ir.id) as total_processed
            FROM patients p
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE 1=1{where_clause}
            """
            cursor.execute(query, params)
            result = cursor.fetchone()

            return {
                'total_patients': total_patients,
                'total_uploads': result['total_uploads'],
                'total_processed': result['total_processed']
            }

        except mysql.connector.Error as e:
            print(f"Error getting filtered overview stats: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_filtered_gender_distribution(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, Any]:
        """Get filtered gender distribution"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            query = f"""
            SELECT p.gender, COUNT(DISTINCT p.patient_id) as count
            FROM patients p
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE p.gender IS NOT NULL{where_clause}
            GROUP BY p.gender
            """
            cursor.execute(query, params)
            results = cursor.fetchall()

            total = sum(row['count'] for row in results)
            gender_data = {}

            for row in results:
                gender_data[row['gender']] = {
                    'count': row['count'],
                    'percentage': round((row['count'] / total * 100), 1) if total > 0 else 0
                }

            return gender_data

        except mysql.connector.Error as e:
            print(f"Error getting filtered gender distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_filtered_tumor_types_distribution(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, Any]:
        """Get filtered tumor types distribution"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            query = f"""
            SELECT ir.predicted_class, COUNT(*) as count
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.predicted_class IS NOT NULL{where_clause}
            GROUP BY ir.predicted_class
            """
            cursor.execute(query, params)
            results = cursor.fetchall()

            total = sum(row['count'] for row in results)
            tumor_data = {}

            for row in results:
                tumor_data[row['predicted_class']] = {
                    'count': row['count'],
                    'percentage': round((row['count'] / total * 100), 1) if total > 0 else 0
                }

            return tumor_data

        except mysql.connector.Error as e:
            print(f"Error getting filtered tumor types distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_filtered_confidence_score_distribution(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, Any]:
        """Get filtered confidence score distribution"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            query = f"""
            SELECT ir.predicted_class, ir.confidence
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.confidence > 0{where_clause}
            """
            cursor.execute(query, params)
            results = cursor.fetchall()

            # Group by predicted class and confidence ranges
            confidence_data = {}

            for row in results:
                tumor_type_key = row['predicted_class']
                confidence = float(row['confidence'])

                if tumor_type_key not in confidence_data:
                    confidence_data[tumor_type_key] = []

                confidence_data[tumor_type_key].append(confidence)

            return confidence_data

        except mysql.connector.Error as e:
            print(f"Error getting filtered confidence distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_filtered_age_distribution(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, int]:
        """Get filtered age distribution"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            query = f"""
            SELECT p.age
            FROM patients p
            LEFT JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE p.age IS NOT NULL{where_clause}
            """
            cursor.execute(query, params)
            results = cursor.fetchall()

            # Group ages into ranges
            age_ranges = {
                '0-20': 0, '21-30': 0, '31-40': 0, '41-50': 0,
                '51-60': 0, '61-70': 0, '71-80': 0, '81+': 0
            }

            for row in results:
                age = row['age']
                if age <= 20:
                    age_ranges['0-20'] += 1
                elif age <= 30:
                    age_ranges['21-30'] += 1
                elif age <= 40:
                    age_ranges['31-40'] += 1
                elif age <= 50:
                    age_ranges['41-50'] += 1
                elif age <= 60:
                    age_ranges['51-60'] += 1
                elif age <= 70:
                    age_ranges['61-70'] += 1
                elif age <= 80:
                    age_ranges['71-80'] += 1
                else:
                    age_ranges['81+'] += 1

            return age_ranges

        except mysql.connector.Error as e:
            print(f"Error getting filtered age distribution: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()

    def get_filtered_brain_tumor_cases_timeline(self, gender=None, tumor_type=None, age_range=None) -> Dict[str, List]:
        """Get filtered brain tumor cases timeline"""
        connection = self.get_db_connection()
        if not connection:
            return {}

        try:
            cursor = connection.cursor(dictionary=True)
            where_clause, params = self._build_filter_conditions(gender, tumor_type, age_range)

            query = f"""
            SELECT
                DATE_FORMAT(ir.scan_date, '%Y-%m') as month,
                COUNT(*) as cases
            FROM patients p
            JOIN images_result ir ON p.patient_id = ir.patient_id
            WHERE ir.scan_date IS NOT NULL
                AND ir.predicted_class IN ('glioma_tumor', 'meningioma_tumor'){where_clause}
            GROUP BY DATE_FORMAT(ir.scan_date, '%Y-%m')
            ORDER BY month
            """
            cursor.execute(query, params)
            results = cursor.fetchall()

            months = [row['month'] for row in results]
            cases = [row['cases'] for row in results]

            return {
                'months': months,
                'cases': cases
            }

        except mysql.connector.Error as e:
            print(f"Error getting filtered timeline: {e}")
            return {}
        finally:
            if connection.is_connected():
                cursor.close()
                connection.close()
