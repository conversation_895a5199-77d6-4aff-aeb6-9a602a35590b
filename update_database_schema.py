#!/usr/bin/env python3
"""
Database Schema Update Script for Google Drive Integration

This script updates the database schema to support Google Drive integration
by adding the necessary columns and tables.
"""

import os
import sys
import mysql.connector
from mysql.connector import Error

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import get_db_connection
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this script from the Radiolens root directory")
    sys.exit(1)

def check_existing_schema():
    """Check if Google Drive columns already exist"""
    print("🔍 Checking existing database schema...")
    
    connection = get_db_connection()
    if not connection:
        print("❌ Cannot connect to database")
        return False
    
    try:
        cursor = connection.cursor()
        
        # Check if Google Drive columns exist in images_result table
        cursor.execute("SHOW COLUMNS FROM images_result LIKE 'drive_file_id'")
        has_drive_columns = cursor.fetchone() is not None
        
        if has_drive_columns:
            print("✅ Google Drive columns already exist in images_result table")
        else:
            print("⚠️ Google Drive columns not found in images_result table")
        
        # Check if new tables exist
        tables_to_check = ['drive_folders', 'drive_files', 'app_settings']
        existing_tables = []
        
        for table in tables_to_check:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
        
        if existing_tables:
            print(f"✅ Found existing tables: {', '.join(existing_tables)}")
        
        missing_tables = [t for t in tables_to_check if t not in existing_tables]
        if missing_tables:
            print(f"⚠️ Missing tables: {', '.join(missing_tables)}")
        
        cursor.close()
        connection.close()
        
        return has_drive_columns and len(missing_tables) == 0
        
    except Error as e:
        print(f"❌ Error checking schema: {e}")
        return False

def backup_database():
    """Create a backup of the current database"""
    print("💾 Creating database backup...")
    
    try:
        # This is a simplified backup - in production, use mysqldump
        connection = get_db_connection()
        if not connection:
            return False
        
        cursor = connection.cursor()
        
        # Get table count for verification
        cursor.execute("SELECT COUNT(*) FROM images_result")
        result_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM patients")
        patient_count = cursor.fetchone()[0]
        
        print(f"✅ Current data: {patient_count} patients, {result_count} analysis results")
        print("⚠️ For production use, create a proper database backup with mysqldump")
        
        cursor.close()
        connection.close()
        return True
        
    except Error as e:
        print(f"❌ Error creating backup: {e}")
        return False

def update_images_result_table():
    """Add Google Drive columns to images_result table"""
    print("🔧 Updating images_result table...")
    
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Add Google Drive columns
        alter_queries = [
            """
            ALTER TABLE images_result
            ADD COLUMN IF NOT EXISTS drive_file_id VARCHAR(255) DEFAULT NULL 
            COMMENT 'Google Drive file ID for the result image'
            """,
            """
            ALTER TABLE images_result
            ADD COLUMN IF NOT EXISTS drive_web_link VARCHAR(512) DEFAULT NULL 
            COMMENT 'Google Drive web view link for the result image'
            """,
            """
            ALTER TABLE images_result
            ADD COLUMN IF NOT EXISTS storage_type ENUM('local', 'google_drive', 'both') DEFAULT 'local' 
            COMMENT 'Storage type for the image'
            """,
            """
            ALTER TABLE images_result
            ADD COLUMN IF NOT EXISTS original_drive_file_id VARCHAR(255) DEFAULT NULL 
            COMMENT 'Google Drive file ID for the original image'
            """,
            """
            ALTER TABLE images_result
            ADD COLUMN IF NOT EXISTS original_drive_web_link VARCHAR(512) DEFAULT NULL 
            COMMENT 'Google Drive web view link for the original image'
            """
        ]
        
        for query in alter_queries:
            try:
                cursor.execute(query)
                print("✅ Added column successfully")
            except Error as e:
                if "Duplicate column name" in str(e):
                    print("⚠️ Column already exists, skipping")
                else:
                    print(f"❌ Error adding column: {e}")
                    return False
        
        # Add indexes
        index_queries = [
            "CREATE INDEX IF NOT EXISTS idx_images_result_drive_file_id ON images_result (drive_file_id)",
            "CREATE INDEX IF NOT EXISTS idx_images_result_storage_type ON images_result (storage_type)"
        ]
        
        for query in index_queries:
            try:
                cursor.execute(query)
                print("✅ Added index successfully")
            except Error as e:
                print(f"⚠️ Index creation warning: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ images_result table updated successfully")
        return True
        
    except Error as e:
        print(f"❌ Error updating images_result table: {e}")
        return False

def create_new_tables():
    """Create new tables for Google Drive integration"""
    print("🔧 Creating new tables...")
    
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Create drive_folders table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS drive_folders (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id VARCHAR(50) NOT NULL,
            folder_name VARCHAR(255) NOT NULL,
            drive_folder_id VARCHAR(255) NOT NULL,
            folder_type ENUM('patient', 'original', 'results') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_patient_folder_type (patient_id, folder_type),
            KEY idx_drive_folder_id (drive_folder_id),
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """)
        print("✅ Created drive_folders table")
        
        # Create drive_files table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS drive_files (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            patient_id VARCHAR(50) NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            drive_file_id VARCHAR(255) NOT NULL,
            drive_web_link VARCHAR(512) DEFAULT NULL,
            local_path VARCHAR(512) DEFAULT NULL,
            file_type ENUM('original', 'result') NOT NULL,
            mime_type VARCHAR(100) DEFAULT NULL,
            file_size INT(11) UNSIGNED DEFAULT NULL,
            upload_status ENUM('pending', 'uploaded', 'failed') DEFAULT 'pending',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_drive_file_id (drive_file_id),
            KEY idx_patient_id (patient_id),
            KEY idx_file_type (file_type),
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """)
        print("✅ Created drive_files table")
        
        # Create app_settings table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS app_settings (
            id INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            setting_key VARCHAR(100) NOT NULL,
            setting_value TEXT DEFAULT NULL,
            setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
            description TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY idx_setting_key (setting_key)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        """)
        print("✅ Created app_settings table")
        
        # Insert default settings
        default_settings = [
            ('google_drive_enabled', 'false', 'boolean', 'Enable Google Drive integration'),
            ('google_drive_root_folder', 'Radiolens', 'string', 'Root folder name in Google Drive'),
            ('use_local_fallback', 'true', 'boolean', 'Use local storage as fallback if Google Drive fails'),
            ('cache_drive_images', 'true', 'boolean', 'Cache Google Drive images locally'),
            ('cache_expiry_hours', '24', 'integer', 'Hours before cached images expire')
        ]
        
        for setting_key, setting_value, setting_type, description in default_settings:
            cursor.execute("""
            INSERT IGNORE INTO app_settings (setting_key, setting_value, setting_type, description)
            VALUES (%s, %s, %s, %s)
            """, (setting_key, setting_value, setting_type, description))
        
        print("✅ Inserted default settings")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ New tables created successfully")
        return True
        
    except Error as e:
        print(f"❌ Error creating new tables: {e}")
        return False

def verify_schema_update():
    """Verify that the schema update was successful"""
    print("🔍 Verifying schema update...")
    
    connection = get_db_connection()
    if not connection:
        return False
    
    try:
        cursor = connection.cursor()
        
        # Check images_result table columns
        cursor.execute("DESCRIBE images_result")
        columns = [row[0] for row in cursor.fetchall()]
        
        required_columns = ['drive_file_id', 'drive_web_link', 'storage_type', 
                          'original_drive_file_id', 'original_drive_web_link']
        
        missing_columns = [col for col in required_columns if col not in columns]
        
        if missing_columns:
            print(f"❌ Missing columns in images_result: {missing_columns}")
            return False
        else:
            print("✅ All required columns exist in images_result table")
        
        # Check new tables
        required_tables = ['drive_folders', 'drive_files', 'app_settings']
        
        for table in required_tables:
            cursor.execute(f"SHOW TABLES LIKE '{table}'")
            if not cursor.fetchone():
                print(f"❌ Missing table: {table}")
                return False
        
        print("✅ All required tables exist")
        
        # Check settings
        cursor.execute("SELECT COUNT(*) FROM app_settings")
        settings_count = cursor.fetchone()[0]
        
        if settings_count > 0:
            print(f"✅ Found {settings_count} default settings")
        else:
            print("⚠️ No default settings found")
        
        cursor.close()
        connection.close()
        
        print("✅ Schema update verification completed successfully")
        return True
        
    except Error as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def main():
    """Main function to update the database schema"""
    print("🚀 Radiolens Database Schema Update for Google Drive Integration")
    print("=" * 70)
    
    # Check if update is needed
    if check_existing_schema():
        print("\n✅ Database schema is already up to date!")
        response = input("Do you want to proceed anyway? (y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            print("Exiting...")
            return True
    
    # Create backup
    print("\n📋 Step 1: Creating backup...")
    if not backup_database():
        print("❌ Backup failed. Exiting for safety.")
        return False
    
    # Update images_result table
    print("\n📋 Step 2: Updating images_result table...")
    if not update_images_result_table():
        print("❌ Failed to update images_result table")
        return False
    
    # Create new tables
    print("\n📋 Step 3: Creating new tables...")
    if not create_new_tables():
        print("❌ Failed to create new tables")
        return False
    
    # Verify update
    print("\n📋 Step 4: Verifying update...")
    if not verify_schema_update():
        print("❌ Schema verification failed")
        return False
    
    print("\n🎉 Database schema update completed successfully!")
    print("✅ Your database is now ready for Google Drive integration")
    print("\nNext steps:")
    print("1. Set up Google Drive credentials")
    print("2. Run: python setup_google_drive.py")
    print("3. Test the integration: python test_google_drive.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
