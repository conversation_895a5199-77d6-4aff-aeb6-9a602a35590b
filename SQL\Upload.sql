-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Dec 12, 2024
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `patient_info`
--

-- --------------------------------------------------------

--
-- Table structure for table `patients`
--

CREATE TABLE `patients` (
  `id` int(11) UNSIGNED NOT NULL,
  `patient_id` varchar(50) NOT NULL,
  `patient_name` varchar(100) NOT NULL,
  `gender` enum('male','female','other') NOT NULL,
  `age` int(3) NOT NULL,
  `date_of_birth` date NOT NULL,
  `radiologist` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `scan_date` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `uploads`
--

CREATE TABLE `uploads` (
  `id` int(11) UNSIGNED NOT NULL,
  `patient_id` varchar(50) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `scan_date` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `images_result`
-- This table stores YOLO model analysis results for uploaded images
--

CREATE TABLE `images_result` (
  `id` int(11) UNSIGNED NOT NULL,
  `patient_id` varchar(50) NOT NULL,
  `patient_name` varchar(100) NOT NULL,
  `result_image` varchar(255) NOT NULL,
  `predicted_class` varchar(100) NOT NULL,
  `confidence` decimal(5,4) NOT NULL,
  `x1` int(11) NOT NULL,
  `y1` int(11) NOT NULL,
  `x2` int(11) NOT NULL,
  `y2` int(11) NOT NULL,
  `width` int(11) NOT NULL,
  `height` int(11) NOT NULL,
  `mask_area` int(11) NOT NULL,
  `location` varchar(255) DEFAULT NULL,
  `scan_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `original_image` varchar(255) DEFAULT NULL,
  `processing_time` decimal(6,3) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `useracc_data`
-- This table stores user account information for login authentication
--

CREATE TABLE `useracc_data` (
  `id` int(11) UNSIGNED NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `user_id` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive') NOT NULL DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Insert default admin account
--
INSERT INTO `useracc_data` (`first_name`, `last_name`, `email`, `user_id`, `password`, `status`) VALUES
('Admin', 'User', '<EMAIL>', 'admin', '1234', 'active');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `patients`
--
ALTER TABLE `patients`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `patient_id` (`patient_id`),
  ADD KEY `idx_patient_id` (`patient_id`);

--
-- Indexes for table `uploads`
--
ALTER TABLE `uploads`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_patient_id` (`patient_id`),
  ADD KEY `patient_id` (`patient_id`);

--
-- Indexes for table `images_result`
--
ALTER TABLE `images_result`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_patient_id` (`patient_id`),
  ADD KEY `idx_predicted_class` (`predicted_class`),
  ADD KEY `idx_scan_date` (`scan_date`),
  ADD KEY `fk_images_result_patient` (`patient_id`);

--
-- Indexes for table `useracc_data`
--
ALTER TABLE `useracc_data`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `patients`
--
ALTER TABLE `patients`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `uploads`
--
ALTER TABLE `uploads`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `images_result`
--
ALTER TABLE `images_result`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `useracc_data`
--
ALTER TABLE `useracc_data`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `uploads`
--
ALTER TABLE `uploads`
  ADD CONSTRAINT `uploads_ibfk_1` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE;

--
-- Constraints for table `images_result`
--
ALTER TABLE `images_result`
  ADD CONSTRAINT `fk_images_result_patient` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`patient_id`) ON DELETE CASCADE ON UPDATE CASCADE;

COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;

-- --------------------------------------------------------
-- TABLE DESCRIPTIONS AND USAGE
-- --------------------------------------------------------

--
-- Table: patients
-- Purpose: Stores patient information including personal details and radiologist assignment
-- Fields: patient_id (unique), patient_name, gender, age, date_of_birth, radiologist
-- Usage: Primary patient data storage, linked to uploads and analysis results
--

--
-- Table: uploads
-- Purpose: Tracks all uploaded image files with metadata
-- Fields: patient_id (FK), file_name, file_path, file_size, file_type, upload_date
-- Usage: File management and tracking, links files to patients
--

--
-- Table: images_result
-- Purpose: Stores YOLO model analysis results for processed medical images
-- Fields: patient_id (FK), patient_name, result_image, predicted_class, confidence,
--         x1, y1, x2, y2 (bounding box), width, height, mask_area, location,
--         analysis_date, original_image, processing_time
-- Usage: AI analysis results storage, automatically populated by Flask app
-- Data Flow: Upload images → YOLO processing → Results saved to this table
--
-- Analysis Results Include:
-- - predicted_class: "No tumor", "Tumor detected", etc.
-- - confidence: Model confidence score (0.0000 to 1.0000)
-- - Bounding box coordinates: x1, y1 (top-left), x2, y2 (bottom-right)
-- - Dimensions: width, height of detected region
-- - mask_area: Area of detected region in pixels
-- - location: Optional tumor location description
-- - processing_time: Analysis duration in seconds
--