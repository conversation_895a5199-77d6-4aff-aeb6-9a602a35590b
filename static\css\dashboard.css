* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

header nav ul li a.active {
    font-weight: bold;
    color: #000000;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 80%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.content {
    background: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
    min-height: 600px;
    padding: 40px;
}

.dashboard-section {
    color: #1B1D43;
}

.dashboard-section h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1B1D43;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
}

.dashboard-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2D3561, #1B1D43);
    border-radius: 2px;
}

/* Top Section Layout */
.top-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 20px;
    align-items: start;
}

/* Dashboard Overview Stats */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.stat-card {
    background: #2D3561;
    border-radius: 8px;
    padding: 12px;
    text-align: center;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 6px;
}

.stat-label {
    font-size: 12px;
    opacity: 0.9;
}

/* Side Slicers Container */
.side-slicers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    height: fit-content;
}

/* Slicer Cards */
.slicer-card {
    background: #1B5E20;
    position: relative;
}

.gender-slicer {
    background: #1B5E20;
}

.tumor-slicer {
    background: #1565C0;
}

.slicer-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: white;
    text-align: center;
}

.slicer-clear {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.slicer-clear:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
}

.slicer-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slicer-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.slicer-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.slicer-btn.active {
    background: rgba(255, 255, 255, 0.9);
    color: #1B5E20;
    border-color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.tumor-slicer .slicer-btn.active {
    color: #1565C0;
}

.slicer-btn:active {
    transform: translateY(0);
}

/* Dropdown Slicers */
.dropdown-slicers {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.dropdown-slicer {
    background: #2D3561;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.dropdown-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: white;
}

.dropdown-select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dropdown-select:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.dropdown-select:focus {
    outline: none;
    border-color: #4ECDC4;
    background: rgba(255, 255, 255, 0.2);
}

.dropdown-select option {
    background: #2D3561;
    color: white;
    padding: 5px;
}

/* Date Range Inputs */
.date-range-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 8px;
    align-items: center;
}

.date-input {
    width: 100%;
    padding: 6px 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 11px;
    transition: all 0.3s ease;
}

.date-input:hover {
    border-color: rgba(255, 255, 255, 0.4);
    background: rgba(255, 255, 255, 0.15);
}

.date-input:focus {
    outline: none;
    border-color: #4ECDC4;
    background: rgba(255, 255, 255, 0.2);
}

.date-input::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
}

.date-separator {
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Charts Grid */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background: #2D3561;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-height: 350px;
}

.chart-container h3 {
    color: white;
    font-size: 18px;
    margin-bottom: 15px;
    text-align: center;
}

.chart-content {
    height: 350px;
    overflow: hidden;
}

/* Timeline section - compact layout */
.timeline-section {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.timeline-container {
    width: 60%;
    min-height: 220px;
    max-width: 600px;
}

.timeline-container h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

.timeline-container .chart-content {
    height: 180px;
}

#timeline-chart {
    height: 180px !important;
}

.chart-content > div {
    height: 100% !important;
}

/* Loading */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: white;
}

/* Error Message */
.error-message {
    text-align: center;
    padding: 40px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 20px 0;
}

.error-message h3 {
    color: #dc3545;
    margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .top-section {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .side-slicers {
        grid-template-columns: 1fr 1fr;
        justify-content: center;
        max-width: 600px;
        margin: 0 auto;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .side-slicers {
        grid-template-columns: 1fr;
        grid-template-rows: 1fr 1fr;
        max-width: 300px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-number {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .stats-overview {
        grid-template-columns: 1fr;
    }
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    position: relative;
    width: 99.4vw;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
    margin-top: 40px;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}

@media (max-width: 768px) {
    footer {
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        padding: 12px 20px;
    }
}