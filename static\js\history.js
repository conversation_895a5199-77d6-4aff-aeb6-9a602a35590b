// Navigation highlighting - Make current page nav link bold
document.addEventListener('DOMContentLoaded', function() {
    // Get current page path
    const currentPath = window.location.pathname;

    // Get all navigation links
    const navLinks = document.querySelectorAll('header nav ul li a');

    // Remove active class from all links first
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // Add active class to current page link
    navLinks.forEach(link => {
        const linkPath = new URL(link.href).pathname;
        const linkText = link.textContent.trim().toLowerCase();

        // Skip "How It Works" links - never make them active/bold
        if (linkText === 'how it works') {
            return;
        }

        if (linkPath === currentPath) {
            link.classList.add('active');
        }
    });
});

// Function to apply filters
function applyFilters() {
    const scanDate = document.getElementById('scan-date').value;
    const patientId = document.getElementById('patient-id').value;
    const patientName = document.getElementById('patient-name').value;
    const statusFilter = document.getElementById('status-filter').value;
    const diagnosisFilter = document.getElementById('diagnosis-filter').value;
    const table = document.getElementById('history-table');
    const rows = table.getElementsByTagName('tr');

    // Loop through all rows (skip header)
    for (let i = 1; i < rows.length; i++) {
        let row = rows[i];
        let dateCell = row.cells[0].textContent; // Scan Date
        let patientIdCell = row.cells[1].textContent;
        let patientNameCell = row.cells[2].textContent;
        let statusCell = row.cells[4].textContent.trim().toLowerCase();
        let diagnosisCell = row.cells[5].textContent.trim().toLowerCase();

        // Filter logic
        let showRow = true;

        if (scanDate && !dateCell.includes(scanDate)) {
            showRow = false;
        }

        if (patientId && !patientIdCell.toLowerCase().includes(patientId.toLowerCase())) {
            showRow = false;
        }

        if (patientName && !patientNameCell.toLowerCase().includes(patientName.toLowerCase())) {
            showRow = false;
        }

        if (statusFilter && !statusCell.includes(statusFilter.toLowerCase())) {
            showRow = false;
        }

        if (diagnosisFilter && !diagnosisCell.includes(diagnosisFilter.toLowerCase())) {
            showRow = false;
        }

        // Show or hide rows based on the filter
        if (showRow) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
}

// Function to view patient report
function viewPatient(patientId, hasAnalysis) {
    if (patientId && patientId !== 'N/A') {
        window.open('/patient/' + patientId + '/report', '_blank');
    } else {
        alert('Patient ID not available');
    }
}

// Function to view raw images for a patient
function viewRawImages(patientId) {
    if (patientId && patientId !== 'N/A') {
        window.open('/patient/' + patientId + '/raw_images', '_blank');
    } else {
        alert('Patient ID not available');
    }
}

// Clear filters function
function clearFilters() {
    // Clear all input fields
    document.getElementById('scan-date').value = '';
    document.getElementById('patient-id').value = '';
    document.getElementById('patient-name').value = '';

    // Reset dropdown selections
    document.getElementById('status-filter').value = '';
    document.getElementById('diagnosis-filter').value = '';

    // Show all rows
    const table = document.getElementById('history-table');
    const rows = table.getElementsByTagName('tr');
    for (let i = 1; i < rows.length; i++) {
        rows[i].style.display = '';
    }
}