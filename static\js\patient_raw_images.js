function openImageComparison(rawFilename, processedFilename, patientId) {
    const modal = document.getElementById('imageModal');
    const rawImage = document.getElementById('rawImage');
    const processedImage = document.getElementById('processedImage');
    const rawPlaceholder = document.getElementById('rawPlaceholder');
    const processedPlaceholder = document.getElementById('processedPlaceholder');

    // Show modal
    modal.style.display = 'block';

    // Reset images
    rawImage.style.display = 'none';
    processedImage.style.display = 'none';
    rawPlaceholder.style.display = 'block';
    processedPlaceholder.style.display = 'block';

    // Load raw image
    if (rawFilename) {
        rawPlaceholder.textContent = 'Loading raw image...';
        rawImage.src = '/raw_image/' + rawFilename;
        rawImage.onload = function() {
            rawPlaceholder.style.display = 'none';
            rawImage.style.display = 'block';
        };
        rawImage.onerror = function() {
            rawPlaceholder.textContent = 'Failed to load raw image';
        };
    } else {
        rawPlaceholder.textContent = 'No raw image available';
    }

    // Load processed image
    if (processedFilename && processedFilename.trim() !== '') {
        processedPlaceholder.textContent = 'Loading processed image...';
        processedImage.src = '/patient_image/' + patientId + '/' + processedFilename;
        processedImage.onload = function() {
            processedPlaceholder.style.display = 'none';
            processedImage.style.display = 'block';
        };
        processedImage.onerror = function() {
            processedPlaceholder.textContent = 'Failed to load processed image';
        };
    } else {
        processedPlaceholder.textContent = 'No processed image available';
    }
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.style.display = 'none';
}

// Close modal when clicking outside of it
window.onclick = function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target == modal) {
        closeImageModal();
    }
}

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeImageModal();
    }
});

// Full Image Modal Functions
function showFullImage(imageSrc, imageTitle) {
    const modal = document.getElementById('fullImageModal');
    const fullImage = document.getElementById('fullImageDisplay');
    const title = document.getElementById('fullImageTitle');

    fullImage.src = imageSrc;
    title.textContent = imageTitle;
    modal.style.display = 'block';

    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';
}

function closeFullImage() {
    const modal = document.getElementById('fullImageModal');
    modal.style.display = 'none';

    // Restore body scrolling
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside the image
document.getElementById('fullImageModal').addEventListener('click', function(event) {
    if (event.target === this) {
        closeFullImage();
    }
});

// Close full image modal with Escape key (in addition to existing modal)
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const fullImageModal = document.getElementById('fullImageModal');
        if (fullImageModal.style.display === 'block') {
            closeFullImage();
        }
    }
});

// Add click event listeners for clickable images
document.addEventListener('DOMContentLoaded', function() {
    const clickableImages = document.querySelectorAll('.clickable-image');
    clickableImages.forEach(function(img) {
        img.addEventListener('click', function() {
            const imageUrl = this.getAttribute('data-image-url');
            const imageTitle = this.getAttribute('data-image-title');
            showFullImage(imageUrl, imageTitle);
        });
    });
});