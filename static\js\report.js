// Image modal functionality
function openImageModal(img) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    modal.style.display = 'block';
    modalImg.src = img.src;
}

function closeImageModal() {
    document.getElementById('imageModal').style.display = 'none';
}

// Carousel functionality
let currentSlide = 0;
let totalSlides = 0;

function initializeCarousel() {
    const slides = document.querySelectorAll('.carousel-slide');
    totalSlides = slides.length;

    if (totalSlides <= 1) {
        // Hide navigation if only one image
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        if (prevBtn) prevBtn.style.display = 'none';
        if (nextBtn) nextBtn.style.display = 'none';
        return;
    }

    updateCarousel();
}

function updateCarousel() {
    // Update slides
    const slides = document.querySelectorAll('.carousel-slide');

    slides.forEach((slide, index) => {
        slide.classList.toggle('active', index === currentSlide);
    });

    // Update counter
    const counter = document.getElementById('current-image');
    if (counter) {
        counter.textContent = currentSlide + 1;
    }

    // Update analysis section
    const activeSlide = slides[currentSlide];
    if (activeSlide) {
        // Update confidence
        const conf = activeSlide.getAttribute('data-confidence');
        const confEl = document.getElementById('analysis-confidence-score');
        if (confEl) confEl.textContent = conf + '%';

        // Update technical details
        const x1 = parseFloat(activeSlide.getAttribute('data-x1'));
        const y1 = parseFloat(activeSlide.getAttribute('data-y1'));
        const x2 = parseFloat(activeSlide.getAttribute('data-x2'));
        const y2 = parseFloat(activeSlide.getAttribute('data-y2'));
        const width = parseFloat(activeSlide.getAttribute('data-width'));
        const height = parseFloat(activeSlide.getAttribute('data-height'));
        const processingTime = activeSlide.getAttribute('data-processing-time');
        const maskArea = parseFloat(activeSlide.getAttribute('data-mask-area'));
        const tumorType = (activeSlide.getAttribute('data-tumor-type') || '').toLowerCase();
        // Update technical details section
        const predictedClass = (activeSlide.getAttribute('data-predicted-class') || '').toLowerCase();
        const techProcessingTime = document.getElementById('tech-processing-time');
        if (techProcessingTime) techProcessingTime.textContent = processingTime ? (processingTime + ' seconds') : '-';
        const techX1 = document.getElementById('tech-x1');
        if (techX1) techX1.textContent = isNaN(x1) ? '-' : x1;
        const techY1 = document.getElementById('tech-y1');
        if (techY1) techY1.textContent = isNaN(y1) ? '-' : y1;
        const techX2 = document.getElementById('tech-x2');
        if (techX2) techX2.textContent = isNaN(x2) ? '-' : x2;
        const techY2 = document.getElementById('tech-y2');
        if (techY2) techY2.textContent = isNaN(y2) ? '-' : y2;
        const techWidth = document.getElementById('tech-width');
        if (techWidth) techWidth.textContent = isNaN(width) ? '-' : width;
        const techHeight = document.getElementById('tech-height');
        if (techHeight) techHeight.textContent = isNaN(height) ? '-' : height;
        const techMaskArea = document.getElementById('tech-mask-area');
        if (techMaskArea) {
            if (predictedClass !== 'no tumor' && predictedClass !== 'no_tumour' && !isNaN(maskArea) && maskArea > 0) {
                techMaskArea.textContent = maskArea + ' px²';
            } else {
                techMaskArea.textContent = '-';
            }
        }
        const techSegPrecision = document.getElementById('tech-seg-precision');
        if (techSegPrecision) {
            if (predictedClass !== 'no tumor' && predictedClass !== 'no_tumour' && !isNaN(maskArea) && !isNaN(width) && !isNaN(height) && width > 0 && height > 0 && maskArea > 0) {
                techSegPrecision.textContent = ((maskArea / (width * height)) * 100).toFixed(1) + '% of bounding box';
            } else {
                techSegPrecision.textContent = '-';
            }
        }

    }

    // Update button states
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (prevBtn) {
        prevBtn.disabled = currentSlide === 0;
    }

    if (nextBtn) {
        nextBtn.disabled = currentSlide === totalSlides - 1;
    }

    // Update tumor location based on current active slide
    calculateTumorLocation();
}

function nextImage() {
    if (currentSlide < totalSlides - 1) {
        currentSlide++;
        updateCarousel();
    }
}

function previousImage() {
    if (currentSlide > 0) {
        currentSlide--;
        updateCarousel();
    }
}

// Touch/Swipe support for mobile
let touchStartX = 0;
let touchEndX = 0;

function handleTouchStart(event) {
    touchStartX = event.changedTouches[0].screenX;
}

function handleTouchEnd(event) {
    touchEndX = event.changedTouches[0].screenX;
    handleSwipe();
}

function handleSwipe() {
    const swipeThreshold = 50;
    const swipeDistance = touchEndX - touchStartX;

    if (Math.abs(swipeDistance) > swipeThreshold) {
        if (swipeDistance > 0) {
            // Swipe right - go to previous image
            previousImage();
        } else {
            // Swipe left - go to next image
            nextImage();
        }
    }
}

// Calculate tumor location based on current active slide
function calculateTumorLocation() {
    // Get all carousel slides
    const slides = document.querySelectorAll('.carousel-slide');
    let currentSlideResult = null;

    // If we have carousel slides, get data from the currently active slide
    if (slides.length > 0) {
        const activeSlide = slides[currentSlide];
        if (activeSlide) {
            currentSlideResult = {
                x1: parseFloat(activeSlide.getAttribute('data-x1')),
                y1: parseFloat(activeSlide.getAttribute('data-y1')),
                x2: parseFloat(activeSlide.getAttribute('data-x2')),
                y2: parseFloat(activeSlide.getAttribute('data-y2')),
                width: parseFloat(activeSlide.getAttribute('data-width')),
                height: parseFloat(activeSlide.getAttribute('data-height')),
                predicted_class: activeSlide.getAttribute('data-predicted-class'),
                confidence: parseFloat(activeSlide.getAttribute('data-confidence'))
            };
        }
    } else {
        // For single image reports, get data from the hidden script element (backward compatibility)
        const singleImageScript = document.getElementById('singleImageData');
        if (singleImageScript) {
            try {
                const singleImageData = JSON.parse(singleImageScript.textContent);
                currentSlideResult = {
                    x1: singleImageData.x1,
                    y1: singleImageData.y1,
                    x2: singleImageData.x2,
                    y2: singleImageData.y2,
                    width: singleImageData.width,
                    height: singleImageData.height,
                    predicted_class: singleImageData.predicted_class,
                    confidence: singleImageData.confidence
                };
            } catch (e) {
                // If parsing fails, return early
                return;
            }
        } else {
            // No data available, return early
            return;
        }
    }

    // Check if we have a valid result
    if (!currentSlideResult) {
        return;
    }

    const { x1, y1, x2, y2, width: imageWidth, height: imageHeight, predicted_class: tumorType } = currentSlideResult;

    // Get the tumor location element
    const locationElement = document.getElementById('analysis-tumor-location');
    if (!locationElement) {
        return;
    }

    // Check for no_tumour cases - set default message and return
    if (tumorType && (tumorType.toLowerCase() === 'no tumor' ||
                     tumorType.toLowerCase() === 'no_tumour' ||
                     tumorType.toLowerCase() === 'no tumor')) {
        locationElement.innerHTML = 'There is no tumor location region detected.';
        return;
    }

    // Validate coordinates - if invalid, don't calculate location
    if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2) ||
        isNaN(imageWidth) || isNaN(imageHeight) ||
        x1 <= 0 || y1 <= 0 || x2 <= 0 || y2 <= 0 ||
        imageWidth <= 0 || imageHeight <= 0) {
        locationElement.innerHTML = 'There is no tumor location region detected.';
        return;
    }

    // Define regions based on relative position using Detection Coordinates
    let location = 'center';

    // Pituitary tumors are always in the center (anatomically correct)
    if (tumorType && tumorType.toLowerCase().includes('pituitary')) {
        location = 'center';
    } else {
        // Calculate center of the bounding box using Detection Coordinates
        const centerX = (x1 + x2) / 2;
        const centerY = (y1 + y2) / 2;

        // Calculate relative position (0-1 range)
        const relativeX = centerX / imageWidth;
        const relativeY = centerY / imageHeight;

        // Divide image into regions for other tumor types
        if (relativeY < 0.4) { // Upper region
            if (relativeX < 0.4) {
                location = 'upper left';
            } else if (relativeX > 0.6) {
                location = 'upper right';
            } else {
                location = 'upper center';
            }
        } else if (relativeY > 0.6) { // Lower region
            if (relativeX < 0.4) {
                location = 'lower left';
            } else if (relativeX > 0.6) {
                location = 'lower right';
            } else {
                location = 'lower center';
            }
        } else { // Middle region
            if (relativeX < 0.35) {
                location = 'center left';
            } else if (relativeX > 0.65) {
                location = 'center right';
            } else {
                location = 'center';
            }
        }
    }

    // Update the location display with calculated location
    locationElement.innerHTML = `The tumor is located in the <span class="tumor-location">${location}</span> region of the brain.`;
}

document.addEventListener('DOMContentLoaded', function () {
    // Initialize carousel
    initializeCarousel();

    // Add touch support for carousel
    const carouselContainer = document.querySelector('.carousel-image-container');
    if (carouselContainer) {
        carouselContainer.addEventListener('touchstart', handleTouchStart, false);
        carouselContainer.addEventListener('touchend', handleTouchEnd, false);
    }

    // Add click handler for single image zoom (backward compatibility)
    const scanImage = document.querySelector('.scan-image:not(.carousel-image)');
    if (scanImage) {
        scanImage.addEventListener('click', function () {
            openImageModal(this);
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
            closeImageModal();
        }

        // Add keyboard navigation for carousel
        if (event.key === 'ArrowLeft') {
            previousImage();
        } else if (event.key === 'ArrowRight') {
            nextImage();
        }
    });



    // Calculate location when page loads
    calculateTumorLocation();
});
