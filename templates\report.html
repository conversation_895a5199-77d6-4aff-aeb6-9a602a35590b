<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Individual Patient Report - RadioLens</title>
  <link rel="stylesheet" href="{{ url_for('static', filename='css/report.css') }}">
</head>


<body>
<!-- Header Section -->
<header>
    <div class="logo">
        <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
    </div>
    <nav>
        <ul>
            <li><a href="{{ url_for('home') }}">Home</a></li>
            <li><a href="{{ url_for('upload') }}">Upload</a></li>
            <li><a href="{{ url_for('report') }}" class="active">Report</a></li>
            <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
            <li><a href="{{ url_for('history') }}">History</a></li>
            <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
        </ul>
    </nav>
</header>

<main class="report-container">
  <h2 class="report-title">Individual Patient Report</h2>

  <div class="report-content">
    <!-- Patient Information Section -->
    <div class="patient-info-container">
      <div class="patient-info-section">
        <div class="patient-details">
        {% if patient_info %}
        <p><strong>Patient Name:</strong> {{ patient_info.patient_name }}</p>
        <p><strong>Age:</strong> {{ patient_info.age }}</p>
        <p><strong>Patient ID:</strong> {{ patient_info.patient_id }}</p>
        <p><strong>Gender:</strong> {{ patient_info.gender|title }}</p>
        {% if patient_info.date_of_birth %}
        <p><strong>D.o.B:</strong> {{ patient_info.date_of_birth.strftime('%d %b %Y') }}</p>
        {% endif %}
        <p><strong>Scan Date:</strong> {{ analysis_date }}</p>
        {% if patient_info.radiologist %}
        <p><strong>Radiologist:</strong> {{ patient_info.radiologist }}</p>
        {% endif %}
        {% else %}
        <p><strong>Patient Name:</strong> Unknown Patient</p>
        <p><strong>Age:</strong> N/A</p>
        <p><strong>Patient ID:</strong> N/A</p>
        <p><strong>Gender:</strong> N/A</p>
        <p><strong>D.o.B:</strong> N/A</p>
        <p><strong>Scan Date:</strong> {{ analysis_date }}</p>
        {% endif %}
        </div>

        <!-- Diagnosis Detail Section -->
        <div class="diagnosis-section">
        <h3>Brain MRI Scan Results</h3>
        {% if all_results and all_results|length > 1 %}
          <!-- Image Carousel -->
          <div class="image-carousel-container">
            <div class="carousel-header">
              <span class="image-counter">
                <span id="current-image">1</span> of {{ all_results|length }}
              </span>
            </div>

            <div class="image-carousel">
              <!-- Navigation Buttons -->
              <button class="carousel-btn carousel-btn-prev" onclick="previousImage()" id="prevBtn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="15,18 9,12 15,6"></polyline>
                </svg>
              </button>

              <!-- Image Display Area -->
              <div class="carousel-image-container">
                {% for result in all_results %}
                <div class="carousel-slide {{ 'active' if loop.first else '' }}"
                  data-slide="{{ loop.index0 }}"
                  data-confidence="{{ (result.confidence * 100) | round(1) }}"
                  data-predicted-class="{{ result.predicted_class }}"
                  data-x1="{{ result.x1 }}"
                  data-y1="{{ result.y1 }}"
                  data-x2="{{ result.x2 }}"
                  data-y2="{{ result.y2 }}"
                  data-width="{{ result.width }}"
                  data-height="{{ result.height }}"
                  data-tumor-type="{{ result.predicted_class }}"
                  data-processing-time="{{ result.processing_time }}"
                  data-mask-area="{{ result.mask_area }}"
                  data-tumor-location="{{ result.tumor_location }}"
                >
                  <div class="scan-image-container" style="position: relative;">
                    <img src="{{ url_for('serve_patient_image', patient_id=patient_info.patient_id if patient_info else 'UNKNOWN', filename=result.result_image) }}"
                         alt="Brain MRI Scan Result {{ loop.index }}"
                         class="scan-image carousel-image"
                         onclick="openImageModal(this)" />
                    <div class="result-overlay">
                      <span class="result-badge {{ 'negative' if result.predicted_class == 'No tumor' else 'positive' }}">
                        {{ result.predicted_class.replace('_', ' ').title() }}
                      </span>
                      <span class="confidence-badge">{{ (result.confidence * 100) | round(1) }}%</span>
                    </div>
                  </div>

                  <!-- Image Details Below -->
                  <div class="carousel-image-details">
                    <div class="detail-row">
                      <span class="detail-label">Result:</span>
                      <span class="detail-value {{ 'negative' if result.predicted_class == 'No tumor' else 'positive' }}">
                        {{ result.predicted_class.replace('_', ' ').title() }}
                      </span>
                    </div>
                    <div class="detail-row">
                      <span class="detail-label">Confidence:</span>
                      <span class="detail-value confidence">{{ (result.confidence * 100) | round(1) }}%</span>
                    </div>
                    {% if result.processing_time %}
                    <div class="detail-row">
                      <span class="detail-label">Processing:</span>
                      <span class="detail-value">{{ result.processing_time }}s</span>
                    </div>
                    {% endif %}
                  </div>
                </div>
                {% endfor %}
              </div>

              <!-- Next Button -->
              <button class="carousel-btn carousel-btn-next" onclick="nextImage()" id="nextBtn">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="9,18 15,12 9,6"></polyline>
                </svg>
              </button>
            </div>


          </div>
        {% else %}
          <!-- Single Image Display -->
          <div class="scan-image-container">
            {% if image_url is defined %}
              <img src="{{ image_url }}"
                   alt="Brain MRI Scan Result"
                   class="scan-image" />
              {% if use_drive_image is defined and use_drive_image %}
                <div class="image-source">Image from Google Drive</div>
              {% endif %}
            {% else %}
              <img src="{{ url_for('serve_patient_image', patient_id=patient_info.patient_id if patient_info else 'UNKNOWN', filename=result_image) }}"
                   alt="Brain MRI Scan Result"
                   class="scan-image" />
            {% endif %}
          </div>

          <!-- Hidden data for single image tumor location calculation -->
          <script type="application/json" id="singleImageData">
          {
            "x1": {{ x1 | default(0) }},
            "y1": {{ y1 | default(0) }},
            "x2": {{ x2 | default(0) }},
            "y2": {{ y2 | default(0) }},
            "width": {{ width | default(512) }},
            "height": {{ height | default(512) }},
            "predicted_class": "{{ predicted_class | default('') }}",
            "confidence": {{ (confidence * 100) | default(0) | round(1) }}
          }
          </script>
        {% endif %}
      </div>
      </div>
    </div>

    <!-- Analysis Section -->
    <div class="analysis-section">
      <h3>Analysis</h3>

      <div class="analysis-results">
        <div class="condition-info">
          <p><strong>Condition:</strong>
            {% if predicted_class == "No tumor" or predicted_class == "no_tumour" or predicted_class.lower() == "no tumor" %}
              The scan result is <span class="condition-negative">negative</span> for brain abnormality.
            {% else %}
              The scan result is <span class="condition-positive">positive</span> for a brain abnormality.
            {% endif %}
          </p>

          <p><strong>Type:</strong>
            {% if overall_diagnosis == "Negative" or not patient_tumor_types %}
              <span class="type-negative">No abnormality detected</span>
            {% elif patient_tumor_types|length == 1 %}
              The detected abnormality has been identified as a <span class="type-positive">{{ patient_tumor_types[0].split(') ')[1] if ') ' in patient_tumor_types[0] else patient_tumor_types[0] }}</span>.
            {% else %}
              The following abnormalities have been detected:
              <div class="multiple-tumor-types">
                {% for tumor_type in patient_tumor_types %}
                <div class="tumor-type-item">
                  <span class="tumor-number">{{ tumor_type.split(')')[0] }})</span>
                  <span class="type-positive">{{ tumor_type.split(') ')[1] if ') ' in tumor_type else tumor_type }}</span>
                </div>
                {% endfor %}
              </div>
            {% endif %}
          </p>

          {% set highest_confidence_result = all_results | selectattr('confidence') | max(attribute='confidence') if all_results else none %}

          <p><strong>Confidence Score:</strong>
            The system reports a confidence score of
            <span class="confidence-score" id="analysis-confidence-score">
              {% if confidence_result %}
                {{ (confidence_result.confidence * 100) | round(1) }}%
              {% else %}
                {{ (confidence * 100) | round(1) }}%
              {% endif %}
            </span>
            for this diagnosis.
          </p>

          <p><strong>Tumor Location:</strong>
            <span id="analysis-tumor-location">
              <!-- Will be populated dynamically by JavaScript based on current slide's detection coordinates -->
            </span>
          </p>
        </div>
      </div>
    </div>

    <!-- Technical Details (Collapsible) -->
    <div class="technical-details">
      <details>
        <summary>Technical Analysis Details</summary>
        <div class="tech-info">
          <p><strong>Processing Time:</strong> <span id="tech-processing-time"></span></p>
          <p><strong>Analysis Date:</strong> <span id="tech-analysis-date">{{ analysis_date }} at {{ scan_time }}</span></p>
          <p><strong>Detection Coordinates:</strong> (<span id="tech-x1"></span>, <span id="tech-y1"></span>) to (<span id="tech-x2"></span>, <span id="tech-y2"></span>)</p>
          <p><strong>Bounding Box Size:</strong> <span id="tech-width"></span> × <span id="tech-height"></span> pixels</p>
          <p><strong>Segmented Tumor Area:</strong> <span id="tech-mask-area"></span></p>
          <p><strong>Segmentation Precision:</strong> <span id="tech-seg-precision"></span></p>
        </div>
      </details>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="action-buttons">
    <a href="{{ url_for('upload') }}" class="btn btn-primary">Upload Another Scan</a>
    <a href="{{ url_for('history') }}" class="btn btn-secondary">View History</a>
    <button onclick="window.print()" class="btn btn-outline">Print Report</button>
  </div>
</main>

<!-- Image Modal -->
<div id="imageModal" class="image-modal" onclick="closeImageModal()">
  <span class="close-modal" onclick="closeImageModal()">&times;</span>
  <img class="modal-content" id="modalImage">
</div>


<footer>
    <p>©2025 RadioLens. All rights reserved.</p>
    <a href="">Privacy Policy</a>
</footer>

</footer>


<script src="{{ url_for('static', filename='js/report.js') }}"></script>

<!-- Navigation highlighting script -->
<script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>

