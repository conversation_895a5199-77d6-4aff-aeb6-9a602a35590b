<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RadioLens History</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/history.css') }}">
</head>

<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('upload') }}">Upload</a></li>
                <li><a href="{{ url_for('report') }}">Report</a></li>
                <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ url_for('history') }}" class="active">History</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="header">
            <h1>RadioLens History</h1>
        </div>



                <!-- Filter Pane -->
                <div class="filter-pane">
                    <div class="filter-group">
                        <label for="scan-date">Scan Date:</label>
                        <input type="date" id="scan-date" placeholder="Select date">
                    </div>

                    <div class="filter-group">
                        <label for="patient-id">Patient ID:</label>
                        <input type="text" id="patient-id" placeholder="Enter Patient ID">
                    </div>

                    <div class="filter-group">
                        <label for="patient-name">Patient Name:</label>
                        <input type="text" id="patient-name" placeholder="Enter Patient Name">
                    </div>

                    <div class="filter-group">
                        <label for="status-filter">Status:</label>
                        <select id="status-filter">
                            <option value="">All Status</option>
                            <option value="uploaded">Uploaded</option>
                            <option value="analyzed">Analyzed</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="diagnosis-filter">Diagnosis:</label>
                        <select id="diagnosis-filter">
                            <option value="">All Diagnosis</option>
                            <option value="positive">Positive</option>
                            <option value="negative">Negative</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>

                    <div class="filter-buttons">
                        <button class="btn-apply" onclick="applyFilters()">Apply Filters</button>
                        <button class="btn-clear" onclick="clearFilters()">Clear Filters</button>
                    </div>
                </div>

                <!-- Table for history -->
                <div class="table-wrapper">
                    <table class="history-table" id="history-table">
                        <thead>
                            <tr>
                                <th>Scan Date</th>
                                <th>Patient ID</th>
                                <th>Patient Name</th>
                                <th>Total Images</th>
                                <th>Status</th>
                                <th>Diagnosis</th>
                                <th>Radiologist</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for patient in patients %}
                            <tr>
                                <td>
                                    {% if patient.last_upload_date %}
                                        {{ patient.last_upload_date.rsplit(' ', 1)[0] if ' ' in patient.last_upload_date else patient.last_upload_date }}
                                    {% endif %}
                                </td>
                                <td>{{ patient.patient_id or 'N/A' }}</td>
                                <td>{{ patient.patient_name or 'Unknown' }}</td>
                                <td>{{ patient.total_uploads }} images</td>
                                <td>
                                    <span class="status-badge status-{{ patient.status.lower() }}">
                                        {{ patient.status }}
                                    </span>
                                </td>
                                <td>
                                    <span class="diagnosis-badge diagnosis-{{ patient.diagnosis.lower() }}">
                                        {{ patient.diagnosis }}
                                    </span>
                                </td>
                                <td>{{ patient.radiologist or 'N/A' }}</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-sm btn-outline" onclick="viewPatient('{{ patient.patient_id }}', {{ patient.has_analysis|lower }})">View Report</button>
                                        <button class="btn btn-sm btn-secondary" onclick="viewRawImages('{{ patient.patient_id }}')">View Raw Images</button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>


    <script src="{{ url_for('static', filename='js/history.js') }}"></script>

     <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>   