<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports Dashboard - RadioLens</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/report_dashboard.css') }}">
</head>


<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('upload') }}">Upload</a></li>
                <li><a href="{{ url_for('report') }}" class="active">Report</a></li>
                <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ url_for('history') }}">History</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <main class="dashboard-container">
        <div class="dashboard-header">
            <h1>Analysis Reports Dashboard</h1>
            <p>View and manage patient analysis reports</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <h3>{{ stats.total_analyses or 0 }}</h3>
                    <p>Total Images Analyses</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>{{ stats.total_patients or 0 }}</h3>
                    <p>Patients Analyzed</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⚠️</div>
                <div class="stat-content">
                    <h3>{{ stats.positive_cases or 0 }}</h3>
                    <p>Positive Cases</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎯</div>
                <div class="stat-content">
                    <h3>{{ ((stats.avg_confidence or 0) * 100) | round(1) }}%</h3>
                    <p>Avg. Confidence Score</p>
                </div>
            </div>
        </div>

        <!-- Filter Pane -->
        <div class="filter-pane">
            <div class="filter-group">
                <label for="scan-date">Scan Date:</label>
                <input type="date" id="scan-date" placeholder="Select date">
            </div>

            <div class="filter-group">
                <label for="patient-id">Patient ID:</label>
                <input type="text" id="patient-id" placeholder="Enter Patient ID">
            </div>

            <div class="filter-group">
                <label for="patient-name">Patient Name:</label>
                <input type="text" id="patient-name" placeholder="Enter Patient Name">
            </div>

            <div class="filter-group">
                <label for="tumor-type">Tumor Type:</label>
                <select id="tumor-type">
                    <option value="">All Types</option>
                    <option value="Glioma Tumor">Glioma Tumor</option>
                    <option value="Meningioma Tumor">Meningioma Tumor</option>
                    <option value="Pituitary Tumor">Pituitary Tumor</option>
                    <option value="No Tumor">No Tumor</option>
                </select>
            </div>

            <div class="filter-buttons">
                <button class="btn-apply" onclick="applyReportFilters()">Apply Filters</button>
                <button class="btn-clear" onclick="clearReportFilters()">Clear Filters</button>
            </div>
        </div>

        <!-- Recent Reports Section -->
        <div class="reports-section">
            <div class="section-header">
                <h2>Recent Analysis Reports</h2>
                <a href="{{ url_for('upload') }}" class="btn btn-primary btn-compact">New Analysis</a>
            </div>

            {% if recent_results %}
            <div class="reports-table-container">
                <table class="reports-table">
                    <thead>
                        <tr>
                            <th>Patient</th>
                            <th>Scan Date</th>
                            <th>Result</th>
                            <th>Tumor Type</th>
                            <th>Confidence Score</th>
                            <th>Radiologist</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for result in recent_results %}
                        <tr>
                            <td>
                                <div class="patient-info">
                                    <strong>{{ result.patient_name }}</strong>
                                    <small>ID: {{ result.patient_id }}</small>
                                    {% if result.total_scans > 1 %}
                                    <small class="scan-count">{{ result.total_scans }} scans</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="date-info">
                                    {{ result.analysis_date_formatted }}
                                </div>
                            </td>
                            <td>
                                <span class="result-badge {{ 'negative' if result.overall_result == 'Negative' else 'positive' }}">
                                    {{ result.overall_result }}
                                </span>
                            </td>
                            <td>
                                <div class="tumor-types-list">
                                    {% if result.tumor_types_list %}
                                        {% for tumor_type in result.tumor_types_list %}
                                        {% set tumor_class = '' %}
                                        {% set tumor_icon = '🧠' %}
                                        {% if 'Glioma' in tumor_type %}
                                            {% set tumor_class = 'glioma' %}
                                            {% set tumor_icon = '🔴' %}
                                        {% elif 'Meningioma' in tumor_type %}
                                            {% set tumor_class = 'meningioma' %}
                                            {% set tumor_icon = '🟡' %}
                                        {% elif 'Pituitary' in tumor_type %}
                                            {% set tumor_class = 'pituitary' %}
                                            {% set tumor_icon = '🟣' %}
                                        {% endif %}
                                        <div class="tumor-item {{ tumor_class }}">
                                            <span class="tumor-number">{{ tumor_type.split(')')[0] }})</span>
                                            <span class="tumor-icon">{{ tumor_icon }}</span>
                                            <span class="tumor-name">{{ tumor_type.split(') ')[1] if ') ' in tumor_type else tumor_type }}</span>
                                        </div>
                                        {% endfor %}
                                    {% else %}
                                        <div class="no-tumors-found">
                                            <span class="icon">✅</span>
                                            <span>No Tumors Detected</span>
                                        </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="confidence-scores-container" data-count="{{ result.confidence_scores_list|length }}">
                                    <!-- Summary header -->
                                    <div class="confidence-summary">
                                        <span>Confidence Scores</span>
                                        <span class="confidence-count">{{ result.confidence_scores_list|length }} scans</span>
                                    </div>

                                    <!-- Grid layout for confidence cards -->
                                    <div class="confidence-grid">
                                        {% for conf in result.confidence_scores_list %}
                                        {% set confidence_class = 'low-confidence' %}
                                        {% if conf.confidence >= 0.8 %}
                                            {% set confidence_class = 'high-confidence' %}
                                        {% elif conf.confidence >= 0.6 %}
                                            {% set confidence_class = 'medium-confidence' %}
                                        {% endif %}
                                        <div class="confidence-card {{ confidence_class }}">
                                            <div class="confidence-header">
                                                <span class="confidence-number">{{ conf.index }}</span>
                                                <span class="confidence-percentage">{{ conf.confidence_pct }}</span>
                                            </div>
                                            <div class="confidence-bar-compact">
                                                <div class="confidence-fill-compact" style="width: {{ ((conf.confidence or 0) * 100) | round(1) }}%"></div>
                                            </div>
                                            <div class="confidence-tumor-label">{{ conf.tumor_name }}</div>
                                        </div>
                                        {% endfor %}
                                    </div>

                                    <!-- Expand toggle for many items -->
                                    <div class="confidence-expand-toggle" onclick="toggleConfidenceExpand(this)">
                                        <span class="expand-text">Show All</span>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="pic-info">
                                    {{ result.radiologist or 'N/A' }}
                                </div>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="{{ url_for('patient_report', patient_id=result.patient_id) }}" class="btn btn-sm btn-outline">View Report</a>
                                    <a href="{{ url_for('patient_raw_images', patient_id=result.patient_id) }}" class="btn btn-sm btn-secondary">Patient History</a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="empty-state">
                <div class="empty-icon">📋</div>
                <h3>No Analysis Reports Found</h3>
                <p>Upload MRI scans to generate analysis reports</p>
                <a href="{{ url_for('upload') }}" class="btn btn-primary">Upload First Scan</a>
            </div>
            {% endif %}
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>
            <div class="action-grid">
                <a href="{{ url_for('upload') }}" class="action-card">
                    <div class="action-icon">📤</div>
                    <h4>Upload New Scan</h4>
                    <p>Analyze new MRI images</p>
                </a>
                <a href="{{ url_for('history') }}" class="action-card">
                    <div class="action-icon">📚</div>
                    <h4>View History</h4>
                    <p>Browse all uploads and results</p>
                </a>
                <a href="{{ url_for('dashboard') }}" class="action-card">
                    <div class="action-icon">📊</div>
                    <h4>Dashboard</h4>
                    <p>System overview and statistics</p>
                </a>
            </div>
        </div>
    </main>
 

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>

    <script src="{{ url_for('static', filename='js/report_dashboard.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
