// Global variables
let rawData = {
    patients: [],
    uploads: [],
    images_result: []
};

let currentFilters = {
    gender: null,
    tumorType: null,
    patientId: null,
    scanDateFrom: null,
    scanDateTo: null
};

// Database connection configuration
const DB_CONFIG = {
    host: 'localhost',
    database: 'patient_info',
    // Note: In production, use proper authentication
};

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('Dashboard initializing...');
    loadDashboardData();
    initializeSlicerInteractions();
    initializeDropdownInteractions();
});

// Load data from database
async function loadDashboardData() {
    try {
        console.log('Loading dashboard data...');

        // Load data from all three tables
        await Promise.all([
            loadPatientsData(),
            loadUploadsData(),
            loadImagesResultData()
        ]);

        console.log('Raw data loaded:', {
            patients: rawData.patients.length,
            uploads: rawData.uploads.length,
            images_result: rawData.images_result.length
        });

        // Update statistics
        updateOverviewStats();

        // Populate dropdown options
        populateDropdownOptions();

        // Create all visualizations
        createAllCharts();

        console.log('Dashboard data loaded successfully');

    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showError('Failed to load dashboard data: ' + error.message);
    }
}

// Load patients data
async function loadPatientsData() {
    try {
        const response = await fetch('/api/patients');
        if (!response.ok) throw new Error('Failed to fetch patients data');
        rawData.patients = await response.json();
        console.log('Patients data loaded:', rawData.patients.length, 'records');
        if (rawData.patients.length > 0) {
            console.log('Sample patient data:', rawData.patients[0]);
        }
    } catch (error) {
        console.error('Error loading patients data:', error);
        // Fallback to empty array
        rawData.patients = [];
    }
}

// Load uploads data
async function loadUploadsData() {
    try {
        const response = await fetch('/api/uploads');
        if (!response.ok) throw new Error('Failed to fetch uploads data');
        rawData.uploads = await response.json();
        console.log('Uploads data loaded:', rawData.uploads.length, 'records');
    } catch (error) {
        console.error('Error loading uploads data:', error);
        rawData.uploads = [];
    }
}

// Load images_result data
async function loadImagesResultData() {
    try {
        const response = await fetch('/api/images_result');
        if (!response.ok) throw new Error('Failed to fetch images_result data');
        rawData.images_result = await response.json();
        console.log('Images result data loaded:', rawData.images_result.length, 'records');
        if (rawData.images_result.length > 0) {
            console.log('Sample images_result data:', rawData.images_result[0]);
        }
    } catch (error) {
        console.error('Error loading images_result data:', error);
        rawData.images_result = [];
    }
}

// Populate dropdown options
function populateDropdownOptions() {
    // Populate Patient ID dropdown
    const patientSelect = document.getElementById('patient-id-filter');
    const uniquePatientIds = [...new Set(rawData.patients.map(p => p.patient_id))].sort();

    // Store current selection
    const currentSelection = patientSelect.value;

    // Clear existing options except "All"
    patientSelect.innerHTML = '<option value="all">All Patients</option>';

    uniquePatientIds.forEach(patientId => {
        const option = document.createElement('option');
        option.value = patientId;
        option.textContent = patientId;
        patientSelect.appendChild(option);
    });

    // Restore previous selection if it still exists
    if (currentSelection && currentSelection !== 'all') {
        const optionExists = patientSelect.querySelector(`option[value="${currentSelection}"]`);
        if (optionExists) {
            patientSelect.value = currentSelection;
            currentFilters.patientId = currentSelection;
        }
    }

    // Set up date range inputs with min/max values from data
    const dateFromInput = document.getElementById('scan-date-from');
    const dateToInput = document.getElementById('scan-date-to');

    const validDates = rawData.patients
        .map(p => p.scan_date ? new Date(p.scan_date).toISOString().substring(0, 10) : null)
        .filter(date => date !== null)
        .sort();

    if (validDates.length > 0) {
        const minDate = validDates[0];
        const maxDate = validDates[validDates.length - 1];

        dateFromInput.min = minDate;
        dateFromInput.max = maxDate;
        dateToInput.min = minDate;
        dateToInput.max = maxDate;
    }
}

// Clear filter function
function clearFilter(filterType) {
    // Reset UI elements
    if (filterType === 'gender') {
        currentFilters.gender = null;
        document.querySelector('#gender-slicer .slicer-btn[data-value="all"]').click();
    } else if (filterType === 'tumorType') {
        currentFilters.tumorType = null;
        document.querySelector('#tumor-slicer .slicer-btn[data-value="all"]').click();
    } else if (filterType === 'patientId') {
        currentFilters.patientId = null;
        const patientSelect = document.getElementById('patient-id-filter');
        patientSelect.value = 'all';
        // Ensure the "All Patients" option is selected and visible
        const allOption = patientSelect.querySelector('option[value="all"]');
        if (allOption) {
            allOption.selected = true;
        }
    } else if (filterType === 'scanDate') {
        currentFilters.scanDateFrom = null;
        currentFilters.scanDateTo = null;
        document.getElementById('scan-date-from').value = '';
        document.getElementById('scan-date-to').value = '';
    }

    // Update dashboard
    updateOverviewStats();
    createAllCharts();
}

// Update overview statistics with filtering
function updateOverviewStats() {
    // Get filtered data based on current slicers
    const filteredPatients = getFilteredPatients();
    const filteredUploads = getFilteredUploads();
    const filteredResults = getFilteredImagesResult();

    // Total patients: Count of unique patient_id from filtered patients
    const totalPatients = filteredPatients.length;

    // Total uploads: Count of rows from filtered uploads
    const totalUploads = filteredUploads.length;

    // Total processed: Count of rows from filtered images_result
    const totalProcessed = filteredResults.length;

    // Calculate new statistics
    const healthyPatients = calculateHealthyPatients(filteredPatients);
    const patientsWithTumors = calculatePatientsWithTumors(filteredPatients);
    const positiveCasesRate = calculatePositiveCasesRate(filteredResults);

    document.getElementById('total-patients').textContent = totalPatients;
    document.getElementById('total-healthy-patients').textContent = healthyPatients;
    document.getElementById('total-patients-with-tumors').textContent = patientsWithTumors;
    document.getElementById('positive-cases-rate').textContent = positiveCasesRate;
    document.getElementById('total-uploads').textContent = totalUploads;
    document.getElementById('total-processed').textContent = totalProcessed;
}

// Calculate healthy patients (patients with only no_tumor/no_tumour results)
function calculateHealthyPatients(filteredPatients) {
    let healthyCount = 0;

    filteredPatients.forEach(patient => {
        // Get all results for this patient
        const patientResults = rawData.images_result.filter(r => r.patient_id === patient.patient_id);

        if (patientResults.length > 0) {
            // Check if all results are no_tumor/no_tumour
            const allHealthy = patientResults.every(result =>
                result.predicted_class === 'no_tumor' ||
                result.predicted_class === 'no_tumour'
            );

            if (allHealthy) {
                healthyCount++;
            }
        }
    });

    return healthyCount;
}

// Calculate patients with tumors (patients with glioma_tumor, meningioma_tumor, or pituitary_tumor)
function calculatePatientsWithTumors(filteredPatients) {
    let tumorCount = 0;

    filteredPatients.forEach(patient => {
        // Get all results for this patient
        const patientResults = rawData.images_result.filter(r => r.patient_id === patient.patient_id);

        if (patientResults.length > 0) {
            // Check if any result shows tumor
            const hasTumor = patientResults.some(result =>
                result.predicted_class === 'glioma_tumor' ||
                result.predicted_class === 'meningioma_tumor' ||
                result.predicted_class === 'pituitary_tumor'
            );

            if (hasTumor) {
                tumorCount++;
            }
        }
    });

    return tumorCount;
}

// Calculate positive cases rate (percentage of each tumor type)
function calculatePositiveCasesRate(filteredResults) {
    if (filteredResults.length === 0) return '0.00%';

    const tumorCounts = {
        glioma_tumor: 0,
        meningioma_tumor: 0,
        pituitary_tumor: 0
    };

    filteredResults.forEach(result => {
        if (tumorCounts.hasOwnProperty(result.predicted_class)) {
            tumorCounts[result.predicted_class]++;
        }
    });

    const totalTumorCases = tumorCounts.glioma_tumor + tumorCounts.meningioma_tumor + tumorCounts.pituitary_tumor;
    const percentage = (totalTumorCases / filteredResults.length) * 100;

    return percentage.toFixed(2) + '%';
}

// Create all charts
function createAllCharts() {
    createGenderChart();
    createTumorTypesChart();
    createConfidenceChart();
    createAgeChart();
    createTimelineChart();
}



// Consistent color mapping for gender and tumor type
const GENDER_COLOR_MAP = {
    'female': '#FF6B6B', // Red
    'male': '#45B7D1',   // Blue
    'other': '#96CEB4',  // Green
    'unknown': '#FFEAA7',
    'Unknown': '#FFEAA7'
};
const TUMOR_COLOR_MAP = {
    'glioma_tumor': '#4ECDC4',
    'meningioma_tumor': '#FF6B6B',
    'pituitary_tumor': '#45B7D1',
    'no_tumour': '#FFEAA7',
    'no_tumor': '#FFEAA7',
    'Unknown': '#DDA0DD'
};

// Create gender distribution pie chart
function createGenderChart() {
    const filteredPatients = getFilteredPatients();
    const genderCounts = {};
    filteredPatients.forEach(patient => {
        let gender = (patient.gender || 'Unknown').toLowerCase();
        genderCounts[gender] = (genderCounts[gender] || 0) + 1;
    });
    const labels = Object.keys(genderCounts);
    const values = Object.values(genderCounts);
    // Use consistent color mapping
    const colors = labels.map(label => GENDER_COLOR_MAP[label] || '#DDA0DD');
    const data = [{
        type: 'pie',
        labels: labels,
        values: values,
        marker: { colors: colors },
        textinfo: 'label+percent',
        textposition: 'outside',
        textfont: { size: 12, color: 'white' },
        hovertemplate: '<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
    }];
    const layout = {
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: 'white' },
        showlegend: true,
        legend: {
            orientation: "v",
            yanchor: "middle",
            y: 0.5,
            xanchor: "left",
            x: 1.02,
            font: { color: 'white', size: 11 },
            bgcolor: 'rgba(0,0,0,0)',
            bordercolor: 'rgba(0,0,0,0)',
            borderwidth: 0
        },
        margin: { l: 40, r: 120, t: 40, b: 40 },
        height: 350
    };
    Plotly.newPlot('gender-chart', data, layout, {responsive: true});
}

// Create tumor types distribution pie chart
function createTumorTypesChart() {
    const filteredResults = getFilteredImagesResult();
    const tumorCounts = {};
    filteredResults.forEach(result => {
        const tumorType = result.predicted_class || 'Unknown';
        tumorCounts[tumorType] = (tumorCounts[tumorType] || 0) + 1;
    });
    const labels = Object.keys(tumorCounts);
    const values = Object.values(tumorCounts);
    // Use consistent color mapping
    const colors = labels.map(label => TUMOR_COLOR_MAP[label] || '#DDA0DD');
    const data = [{
        type: 'pie',
        labels: labels,
        values: values,
        hole: 0.3,
        marker: { colors: colors },
        textinfo: 'percent',
        textposition: 'inside',
        textfont: { size: 11, color: 'white' },
        hovertemplate: '<b>%{label}</b><br>Count: %{value}<br>Percentage: %{percent}<extra></extra>'
    }];
    const layout = {
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: 'white' },
        showlegend: true,
        legend: {
            orientation: "v",
            yanchor: "middle",
            y: 0.5,
            xanchor: "left",
            x: 1.02,
            font: { color: 'white', size: 11 },
            bgcolor: 'rgba(0,0,0,0)',
            bordercolor: 'rgba(0,0,0,0)',
            borderwidth: 0
        },
        margin: { l: 40, r: 120, t: 40, b: 40 },
        height: 350
    };
    Plotly.newPlot('tumor-types-chart', data, layout, {responsive: true});
}

// Create confidence score distribution heatmap
function createConfidenceChart() {
    const filteredResults = getFilteredImagesResult();

    // Group by predicted class and confidence ranges
    const confidenceRanges = ['0.0-0.2', '0.2-0.4', '0.4-0.6', '0.6-0.8', '0.8-1.0'];
    const tumorTypes = [...new Set(filteredResults.map(r => r.predicted_class))];

    const heatmapData = [];
    tumorTypes.forEach(tumorType => {
        const row = [];
        confidenceRanges.forEach(range => {
            const [min, max] = range.split('-').map(parseFloat);
            const count = filteredResults.filter(r =>
                r.predicted_class === tumorType &&
                r.confidence >= min &&
                r.confidence < max
            ).length;
            row.push(count);
        });
        heatmapData.push(row);
    });

    const data = [{
        type: 'heatmap',
        z: heatmapData,
        x: confidenceRanges,
        y: tumorTypes,
        colorscale: 'Viridis',
        showscale: true,
        colorbar: {
            title: { text: "Count", font: { color: 'white' } },
            tickfont: { color: 'white' }
        }
    }];

    const layout = {
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: 'white' },
        xaxis: {
            title: { text: 'Confidence Range', font: { color: 'white' } },
            tickfont: { color: 'white', size: 11 }
        },
        yaxis: {
            title: { text: 'Predicted Class', font: { color: 'white' } },
            tickfont: { color: 'white', size: 10 }
        },
        margin: { l: 120, r: 50, t: 30, b: 50 },
        height: 320
    };

    Plotly.newPlot('confidence-chart', data, layout, {responsive: true});
}

// Create age distribution bar chart
function createAgeChart() {
    const filteredPatients = getFilteredPatients();

    // Define age ranges
    const ageRanges = {
        '0-20': 0, '21-30': 0, '31-40': 0, '41-50': 0, '51-60': 0,
        '61-70': 0, '71-80': 0, '81-90': 0, '91-100': 0
    };

    // Categorize patients by age
    filteredPatients.forEach(patient => {
        const age = parseInt(patient.age);
        if (age >= 0 && age <= 20) ageRanges['0-20']++;
        else if (age >= 21 && age <= 30) ageRanges['21-30']++;
        else if (age >= 31 && age <= 40) ageRanges['31-40']++;
        else if (age >= 41 && age <= 50) ageRanges['41-50']++;
        else if (age >= 51 && age <= 60) ageRanges['51-60']++;
        else if (age >= 61 && age <= 70) ageRanges['61-70']++;
        else if (age >= 71 && age <= 80) ageRanges['71-80']++;
        else if (age >= 81 && age <= 90) ageRanges['81-90']++;
        else if (age >= 91 && age <= 100) ageRanges['91-100']++;
    });

    const ageLabels = Object.keys(ageRanges);
    const ageCounts = Object.values(ageRanges);

    const data = [{
        type: 'bar',
        x: ageLabels,
        y: ageCounts,
        marker: { color: '#4ECDC4' },
        text: ageCounts,
        textposition: 'outside',
        textfont: { color: 'white' },
        hovertemplate: '<b>Age Range: %{x}</b><br>Count: %{y}<extra></extra>'
    }];

    const layout = {
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: 'white' },
        xaxis: {
            title: { text: 'Age Range', font: { color: 'white' } },
            tickfont: { color: 'white' },
            gridcolor: 'rgba(255,255,255,0.2)'
        },
        yaxis: {
            title: { text: 'Number of Patients', font: { color: 'white' } },
            tickfont: { color: 'white' },
            gridcolor: 'rgba(255,255,255,0.2)',
            range: [0, Math.max(...ageCounts) + 1]
        },
        margin: { l: 50, r: 20, t: 40, b: 50 },
        height: 320
    };

    Plotly.newPlot('age-chart', data, layout, {responsive: true});
}

// Create brain tumor cases timeline line chart (compact size)
function createTimelineChart() {
    const filteredResults = getFilteredImagesResult();

    // Group by scan date (daily aggregation - Year-Month-Day)
    const timelineData = {};

    filteredResults.forEach(result => {
        if (result.scan_date && result.predicted_class) {
            const date = new Date(result.scan_date);
            const dateKey = date.toISOString().substring(0, 10); // YYYY-MM-DD format (no time)

            if (!timelineData[dateKey]) {
                timelineData[dateKey] = 0;
            }

            // Count total cases per date (all tumor types combined)
            timelineData[dateKey]++;
        }
    });

    // Sort dates and prepare data
    const sortedDates = Object.keys(timelineData).sort();
    const caseCounts = sortedDates.map(date => timelineData[date]);

    // Create single line trace for total cases
    const trace = {
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Total Cases',
        x: sortedDates,
        y: caseCounts,
        line: {
            width: 2,
            color: '#4ECDC4'
        },
        marker: {
            size: 3,
            color: '#4ECDC4'
        },
        hovertemplate: '<b>Date: %{x}</b><br>Cases: %{y}<extra></extra>'
    };

    const layout = {
        paper_bgcolor: 'rgba(0,0,0,0)',
        plot_bgcolor: 'rgba(0,0,0,0)',
        font: { color: 'white', size: 8 },
        xaxis: {
            title: { text: 'Scan Date', font: { color: 'white', size: 9 } },
            tickfont: { color: 'white', size: 7 },
            gridcolor: 'rgba(255,255,255,0.1)',
            tickangle: -45
        },
        yaxis: {
            title: { text: 'Count', font: { color: 'white', size: 9 } },
            tickfont: { color: 'white', size: 7 },
            gridcolor: 'rgba(255,255,255,0.1)'
        },
        showlegend: false,
        margin: { l: 30, r: 10, t: 10, b: 50 },
        height: 180
    };

    Plotly.newPlot('timeline-chart', [trace], layout, {responsive: true});
}

// Filter functions
function getFilteredPatients() {
    let filtered = rawData.patients;

    if (currentFilters.gender && currentFilters.gender !== 'all') {
        filtered = filtered.filter(p => p.gender === currentFilters.gender);
    }

    if (currentFilters.patientId && currentFilters.patientId !== 'all') {
        filtered = filtered.filter(p => p.patient_id === currentFilters.patientId);
    }

    // Date range filtering
    if (currentFilters.scanDateFrom || currentFilters.scanDateTo) {
        filtered = filtered.filter(p => {
            if (!p.scan_date) return false;
            const patientDate = new Date(p.scan_date).toISOString().substring(0, 10);

            let withinRange = true;
            if (currentFilters.scanDateFrom) {
                withinRange = withinRange && patientDate >= currentFilters.scanDateFrom;
            }
            if (currentFilters.scanDateTo) {
                withinRange = withinRange && patientDate <= currentFilters.scanDateTo;
            }
            return withinRange;
        });
    }

    // If tumor type filter is applied, only include patients who have results with that tumor type
    if (currentFilters.tumorType && currentFilters.tumorType !== 'all') {
        const patientIdsWithTumorType = rawData.images_result
            .filter(r => r.predicted_class === currentFilters.tumorType)
            .map(r => r.patient_id);
        filtered = filtered.filter(p => patientIdsWithTumorType.includes(p.patient_id));
    }

    return filtered;
}

function getFilteredUploads() {
    let filtered = rawData.uploads;

    // Filter by patient ID
    if (currentFilters.patientId && currentFilters.patientId !== 'all') {
        filtered = filtered.filter(u => u.patient_id === currentFilters.patientId);
    }

    // Filter by scan date range
    if (currentFilters.scanDateFrom || currentFilters.scanDateTo) {
        filtered = filtered.filter(u => {
            if (!u.upload_date) return false;
            const uploadDate = new Date(u.upload_date).toISOString().substring(0, 10);

            let withinRange = true;
            if (currentFilters.scanDateFrom) {
                withinRange = withinRange && uploadDate >= currentFilters.scanDateFrom;
            }
            if (currentFilters.scanDateTo) {
                withinRange = withinRange && uploadDate <= currentFilters.scanDateTo;
            }
            return withinRange;
        });
    }

    // If gender filter is applied, only include uploads from patients with that gender
    if (currentFilters.gender && currentFilters.gender !== 'all') {
        const patientIdsWithGender = rawData.patients
            .filter(p => p.gender === currentFilters.gender)
            .map(p => p.patient_id);
        filtered = filtered.filter(u => patientIdsWithGender.includes(u.patient_id));
    }

    // Do NOT filter by tumor class for uploads
    return filtered;
}

function getFilteredImagesResult() {
    let filtered = rawData.images_result;

    // Filter by tumor type
    if (currentFilters.tumorType && currentFilters.tumorType !== 'all') {
        filtered = filtered.filter(r => r.predicted_class === currentFilters.tumorType);
    }

    // Filter by patient ID
    if (currentFilters.patientId && currentFilters.patientId !== 'all') {
        filtered = filtered.filter(r => r.patient_id === currentFilters.patientId);
    }

    // Filter by scan date range
    if (currentFilters.scanDateFrom || currentFilters.scanDateTo) {
        filtered = filtered.filter(r => {
            if (!r.scan_date) return false;
            const resultDate = new Date(r.scan_date).toISOString().substring(0, 10);

            let withinRange = true;
            if (currentFilters.scanDateFrom) {
                withinRange = withinRange && resultDate >= currentFilters.scanDateFrom;
            }
            if (currentFilters.scanDateTo) {
                withinRange = withinRange && resultDate <= currentFilters.scanDateTo;
            }
            return withinRange;
        });
    }

    // If gender filter is applied, only include results from patients with that gender
    if (currentFilters.gender && currentFilters.gender !== 'all') {
        const patientIdsWithGender = rawData.patients
            .filter(p => p.gender === currentFilters.gender)
            .map(p => p.patient_id);
        filtered = filtered.filter(r => patientIdsWithGender.includes(r.patient_id));
    }

    return filtered;
}

// Initialize slicer interactions
function initializeSlicerInteractions() {
    // Add click handlers to slicer buttons
    const slicerButtons = document.querySelectorAll('.slicer-btn');
    slicerButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const filterType = this.dataset.filter;
            const value = this.dataset.value === 'all' ? null : this.dataset.value;

            // Update filter
            currentFilters[filterType] = value;

            // Update button states
            updateSlicerButtonStates();

            // Update statistics and charts
            updateOverviewStats();
            createAllCharts();
        });
    });

    // Set initial active states
    updateSlicerButtonStates();
}

// Initialize dropdown interactions
function initializeDropdownInteractions() {
    // Patient ID dropdown
    const patientIdSelect = document.getElementById('patient-id-filter');
    patientIdSelect.addEventListener('change', function() {
        currentFilters.patientId = this.value === 'all' ? null : this.value;

        // Keep the selected value visible in the dropdown
        if (this.value !== 'all') {
            // Find the selected option and ensure it stays selected
            const selectedOption = this.querySelector(`option[value="${this.value}"]`);
            if (selectedOption) {
                selectedOption.selected = true;
            }
        }

        updateOverviewStats();
        createAllCharts();
    });

    // Scan Date range inputs
    const scanDateFrom = document.getElementById('scan-date-from');
    const scanDateTo = document.getElementById('scan-date-to');

    scanDateFrom.addEventListener('change', function() {
        currentFilters.scanDateFrom = this.value || null;
        updateOverviewStats();
        createAllCharts();
    });

    scanDateTo.addEventListener('change', function() {
        currentFilters.scanDateTo = this.value || null;
        updateOverviewStats();
        createAllCharts();
    });
}

// Update slicer button states
function updateSlicerButtonStates() {
    const slicerButtons = document.querySelectorAll('.slicer-btn');
    slicerButtons.forEach(btn => {
        const filterType = btn.dataset.filter;
        const value = btn.dataset.value === 'all' ? null : btn.dataset.value;

        if (currentFilters[filterType] === value) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

// Show error message
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <h3>Error</h3>
        <p>${message}</p>
    `;

    const content = document.querySelector('.content');
    content.insertBefore(errorDiv, content.firstChild);
}

// Auto-refresh every 30 seconds
setInterval(() => {
    console.log('Auto-refreshing dashboard data...');
    loadDashboardData();
}, 30000);