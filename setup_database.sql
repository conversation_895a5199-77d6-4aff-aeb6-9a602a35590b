-- Radiolens Database Setup for phpMyAdmin
-- Run this script in phpMyAdmin to manually create the database and tables

-- Create database
CREATE DATABASE IF NOT EXISTS patient_info;
USE patient_info;

-- Create patients table
CREATE TABLE IF NOT EXISTS patients (
    patient_id VARCHAR(50) PRIMARY KEY,
    patient_name VARCHAR(100) NOT NULL,
    gender ENUM('male', 'female', 'other') NOT NULL,
    age INT NOT NULL,
    date_of_birth DATE NOT NULL,
    radiologist <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_patient_name (patient_name),
    INDEX idx_gender (gender)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;





-- Show created tables
SHOW TABLES;

-- Display table structures
DESCRIBE patients;

-- Success message
SELECT 'Database setup completed successfully!' as Status;
