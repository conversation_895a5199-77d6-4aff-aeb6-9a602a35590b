/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header Navigation */
/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

/* Main Dashboard Container */
.dashboard-container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 93%;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 40px;
}

.dashboard-header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.dashboard-header p {
    color: #fff;
    opacity: 0.9;
    font-size: 18px;
    margin-top: 10px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f8ff;
    border-radius: 50%;
}

.stat-content h3 {
    font-size: 28px;
    color: #1976d2;
    margin-bottom: 5px;
}

.stat-content p {
    color: #666;
    font-size: 14px;
}

/* Reports Section */
.reports-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    color: #333;
    font-size: 24px;
}

/* Result badge styling with color coordination */
.result-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-badge.positive {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

.result-badge.negative {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
    box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);
}

/* Enhanced tumor types list styling */
.tumor-types-list {
    max-width: 220px;
    min-width: 180px;
}

.tumor-item {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin-bottom: 6px;
    padding: 6px 10px;
    font-size: 12px;
    line-height: 1.4;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9ff, #e8f2ff);
    border-left: 3px solid #667eea;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.tumor-item:hover {
    transform: translateX(3px) scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tumor-item:last-child {
    margin-bottom: 0;
}

/* Tumor type specific styling */
.tumor-item.glioma {
    background: linear-gradient(135deg, #fff5f5, #ffe8e8);
    border-left-color: #e53e3e;
    color: #c53030;
}

.tumor-item.meningioma {
    background: linear-gradient(135deg, #fff8e1, #ffecb3);
    border-left-color: #ff8f00;
    color: #e65100;
}

.tumor-item.pituitary {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    border-left-color: #8e24aa;
    color: #6a1b9a;
}

.tumor-item.no-tumor {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    border-left-color: #4caf50;
    color: #2e7d32;
}

/* Tumor type icons */
.tumor-icon {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.tumor-number {
    font-weight: 600;
    margin-right: 6px;
    min-width: 18px;
    text-align: center;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
}

.tumor-name {
    flex: 1;
    font-weight: 500;
}

/* Empty state for no tumors */
.no-tumors-found {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    color: #6c757d;
    font-style: italic;
    font-size: 12px;
}

.no-tumors-found .icon {
    margin-right: 6px;
    font-size: 14px;
}

/* Enhanced confidence bar */
.confidence-bar {
    position: relative;
    background: #f8f9fa;
    border-radius: 10px;    
    height: 24px;
    overflow: hidden;
    min-width: 120px;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.confidence-text {
    position: absolute;
    top: 50%;
    left: 8px;
    transform: translateY(-50%);
    font-weight: 600;
    font-size: 11px;
    color: #2c3e50;
    z-index: 2;
}

.confidence-label {
    display: block;
    font-size: 10px;
    color: #7f8c8d;
    margin-top: 2px;
    text-align: center;
}


/* Optimized column widths for better layout */
.reports-table th:nth-child(1) { width: 13%; } /* Patient */
.reports-table th:nth-child(2) { width: 11%; } /* Analysis Date */
.reports-table th:nth-child(3) { width: 12%; } /* Result */
.reports-table th:nth-child(4) { width: 20%; } /* Tumor Type */
.reports-table th:nth-child(5) { width: 28%; } /* Confidence Score */
.reports-table th:nth-child(6) { width: 10%; } /* P.I.C */
.reports-table th:nth-child(7) { width: 10%; } /* Actions */

.reports-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
    vertical-align: middle;
    text-align: center;
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Specific cell alignments */
.reports-table td:nth-child(1) { text-align: left; } /* Patient */
.reports-table td:nth-child(4) { text-align: left; } /* Tumor Type */

.reports-table tbody tr:hover {
    background-color: #f8f9ff;
    cursor: pointer;
}

/* Patient info styling */
.patient-info {
    text-align: left;
}

.patient-info strong {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    font-size: 13px;
    line-height: 1.2;
}

.patient-info small {
    display: block;
    color: #7f8c8d;
    font-size: 10px;
    line-height: 1.1;
}

.scan-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px !important;
    margin-top: 2px;
    display: inline-block;
}

/* Date info styling */
.date-info {
    color: #333;
    font-size: 11px;
    font-weight: 600;
    background-color: #f8f9ff;
    padding: 4px 6px;
    border-radius: 4px;
    text-align: center;
    border: 1px solid #e3f2fd;
    white-space: nowrap;
}


/* Button Styles */
.btn {
    padding: 10px 20px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: #1976d2;
    color: white;
}

.btn-primary:hover {
    background: #1565c0;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #757575;
    color: white;
}

.btn-secondary:hover {
    background: #616161;
}

.btn-outline {
    background: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.btn-outline:hover {
    background: #1976d2;
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Compact button styling for very short width */
.btn-compact {
    padding: 16px 10px !important;
    font-size: 13px !important;
    font-weight: 600;
    white-space: nowrap;
    min-width: 60px;
    max-width: 100px;
    border-radius: 4px;
    text-align: center;
}

/* Reports Table */
.reports-table-container {
    overflow-x: auto;
}

.reports-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.reports-table th,
.reports-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.reports-table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    border: none;
    font-size: 12px;
    white-space: nowrap;
}

.reports-table tbody tr {
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.reports-table tbody tr:hover {
    background-color: #f8f9fa;
}

.patient-info strong {
    display: block;
    color: #333;
}

.patient-info small {
    color: #666;
    font-size: 12px;
}

.date-info small {
    display: block;
    color: #666;
    font-size: 12px;
}

.result-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.result-badge.negative {
    background: #e8f5e8;
    color: #2e7d32;
}

.result-badge.positive {
    background: #fff3e0;
    color: #f57c00;
}

.confidence-bar {
    position: relative;
    background: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    width: 100px;
}

.confidence-fill {
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
}

.confidence-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 11px;
    font-weight: 600;
    color: #333;
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

.empty-state p {
    margin-bottom: 30px;
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    background: #e9ecef;
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 32px;
    margin-bottom: 15px;
}

.action-card h4 {
    margin-bottom: 10px;
    color: #1976d2;
}

.action-card p {
    font-size: 14px;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }
    
    nav ul {
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-container {
        padding: 20px 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .reports-table-container {
        font-size: 14px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}

        /* Enhanced confidence scores - Grid Layout */
        .confidence-scores-container {
            max-width: 100%;
            min-width: 200px;
            width: 100%;
        }

        .confidence-summary {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 6px 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            color: white;
            font-size: 11px;
            font-weight: 600;
        }

        .confidence-count {
            background: rgba(255, 255, 255, 0.2);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        /* Grid layout for confidence scores */
        .confidence-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(85px, 1fr));
            gap: 4px;
            max-height: 100px;
            overflow-y: auto;
            padding: 2px;
        }

        /* Custom scrollbar for grid */
        .confidence-grid::-webkit-scrollbar {
            width: 3px;
        }

        .confidence-grid::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 2px;
        }

        .confidence-grid::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 2px;
        }

        .confidence-grid::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .confidence-card {
            background: white;
            border-radius: 8px;
            padding: 8px;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .confidence-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .confidence-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--confidence-color);
        }

        .confidence-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 6px;
        }

        .confidence-number {
            background: #667eea;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 9px;
            font-weight: 700;
            flex-shrink: 0;
        }

        .confidence-percentage {
            font-weight: 700;
            font-size: 12px;
            color: var(--confidence-text-color);
        }

        .confidence-bar-compact {
            height: 6px;
            background: #f1f5f9;
            border-radius: 3px;
            overflow: hidden;
            margin: 4px 0;
        }

        .confidence-fill-compact {
            height: 100%;
            border-radius: 3px;
            transition: width 0.4s ease;
            background: var(--confidence-color);
        }

        .confidence-tumor-label {
            font-size: 9px;
            color: #64748b;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
            margin-top: 2px;
        }

        /* Color variables for different confidence levels */
        .confidence-card.high-confidence {
            --confidence-color: linear-gradient(90deg, #10b981, #059669);
            --confidence-text-color: #059669;
        }

        .confidence-card.medium-confidence {
            --confidence-color: linear-gradient(90deg, #f59e0b, #d97706);
            --confidence-text-color: #d97706;
        }

        .confidence-card.low-confidence {
            --confidence-color: linear-gradient(90deg, #ef4444, #dc2626);
            --confidence-text-color: #dc2626;
        }

        /* Expandable view for many scores */
        .confidence-expand-toggle {
            display: none;
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 10px;
            color: #667eea;
            cursor: pointer;
            margin-top: 4px;
            text-align: center;
            transition: all 0.2s ease;
        }

        .confidence-expand-toggle:hover {
            background: #667eea;
            color: white;
        }

        /* Show expand button when there are more than 4 items */
        .confidence-scores-container[data-count="5"] .confidence-expand-toggle,
        .confidence-scores-container[data-count="6"] .confidence-expand-toggle,
        .confidence-scores-container[data-count="7"] .confidence-expand-toggle,
        .confidence-scores-container[data-count="8"] .confidence-expand-toggle,
        .confidence-scores-container[data-count="9"] .confidence-expand-toggle,
        .confidence-scores-container[data-count="10"] .confidence-expand-toggle {
            display: block;
        }

        /* Responsive design for confidence scores */
        @media (max-width: 1200px) {
            .confidence-grid {
                grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
                gap: 4px;
            }

            .confidence-card {
                padding: 6px;
            }

            .confidence-percentage {
                font-size: 11px;
            }

            .confidence-tumor-label {
                font-size: 8px;
            }
        }

        @media (max-width: 768px) {
            .confidence-scores-container {
                max-width: 250px;
                min-width: 200px;
            }

            .confidence-grid {
                grid-template-columns: repeat(2, 1fr);
                max-height: 100px;
            }

            .confidence-summary {
                font-size: 10px;
                padding: 4px 8px;
            }
        }

        /* P.I.C styling */
        .pic-info {
            font-weight: 500;
            color: #2c3e50;
        }

        /* Action buttons styling */
        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 6px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            white-space: nowrap;
            width: 100%;
            text-align: center;
            min-width: 120px;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        /* Filter Pane */
        .filter-pane {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 20px;
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 150px;
        }

        .filter-pane label {
            font-weight: 600;
            color: #1B1D43;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-pane input {
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background: #ffffff;
            color: #333;
            transition: all 0.3s ease;
        }

        .filter-pane input:focus {
            outline: none;
            border-color: #2D3561;
            box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
        }

        .filter-pane select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            background: #ffffff;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }

        .filter-pane select:hover {
            border-color: #2D3561;
            background-color: #f8f9fa;
        }

        .filter-pane select:focus {
            outline: none;
            border-color: #2D3561;
            box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
            background-color: #ffffff;
        }

        .filter-buttons {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
            min-width: 120px;
        }

        .btn-apply, .btn-clear {
            padding: 10px 16px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            white-space: nowrap;
        }

        .btn-apply {
            background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
            color: white;
        }

        .btn-apply:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(45, 53, 97, 0.4);
        }

        .btn-clear {
            background: #dc3545;
            color: white;
        }

        .btn-clear:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(2, 1fr);
            gap: 20px;
            padding: 30px;
        }

        /* Footer */
        footer {
            text-align: center;
            padding: 15px 20px;
            font-size: 12px;
            background-color: #fff;
            border-top: 1px solid #ccc;
            color: #1B1D43;
            width: 103.9%;
            margin: 0;
            margin-top: 50px;
            position: relative;
            left: 50%;
            right: 90%;
            margin-left: -50vw;
            margin-right: -30vw;
        }

        footer a {
            color: #1B1D43;
            text-decoration: underline;
            margin-left: 10px;
            transition: color 0.3s ease;
        }

        footer a:hover {
            color: #007BFF;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            header {
                padding: 8px;
                flex-wrap: wrap;
            }

            header .logo img {
                width: 50px;
                margin-left: 15px;
                max-height: 40px;
            }

            header nav ul {
                flex-wrap: wrap;
                gap: 10px;
            }

            header nav ul li {
                margin-right: 15px;
            }

            header nav ul li a {
                font-size: 14px;
            }

            body {
                padding: 120px 20px 0 20px;
            }

            .dashboard-container {
                width: 95%;
            }

            .dashboard-header h1 {
                font-size: 28px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, 1fr);
                gap: 15px;
                padding: 20px;
            }

            .filter-pane {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                padding: 15px;
            }

            .filter-group {
                min-width: auto;
            }

            .filter-buttons {
                flex-direction: column;
                align-items: stretch;
                margin-top: 10px;
                min-width: auto;
            }

            .reports-table-container {
                overflow-x: auto;
            }

            .reports-table {
                min-width: 800px;
                font-size: 11px;
            }

            .reports-table th,
            .reports-table td {
                padding: 8px 4px;
                font-size: 10px;
            }

            footer {
                left: 50%;
                right: 50%;
                margin-left: -50vw;
                margin-right: -50vw;
                padding: 12px 20px;
                font-size: 11px;
                width: 100vw;
            }
        }