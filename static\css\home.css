/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Body and Background */
body {
    font-family: 'Helvetica Neue', sans-serif;
    background-color: #fff; /* Light off-white background */
    height: 100vh; /* Full viewport height */
    display: flex;
    flex-direction: column;
    width: 100%; /* Make body take full width */
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #fff; /* Light color background */
    width: 100%;
    z-index: 10;
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
}

header nav ul {
    display: flex;
    list-style-type: none;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;    
}

/* Log In Button */
header nav ul li .login-btn {
    background-color: #007BFF;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
}

header nav ul li .login-btn:hover {
    background-color: #d6dfea;
}

/* Landing Section */
.landing-page {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%; /* Ensure full width */
    height: 100%; /* Full height of the viewport */
    padding: 0; /* Remove padding */
    margin-top: 0; /* Remove any top margin */
}

.left-container {
    width: 50%; /* Adjusted to 45% for left container */
    padding: 70px;
    background-color: #1B1D43; /* Dark blue background */
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 140%; /* Full height */
}

.left-container h2 {
    font-size: 50px;
    margin-bottom: 20px;
    margin-top: 35%;
}

.left-container p {
    font-size: 18px;
    margin-bottom: 20px;
    line-height: 30px;
}

.register-btn {
    padding: 15px 30px;
    font-size: 18px;
    background-color: #28a745;
    color: white;
    border: none;
    cursor: pointer;
}


.right-container {
    width: 60%; /* Adjust width to 60% */
    padding: 0; /* Remove padding */
    height: 100%; /* Set height to 140% */
}

.right-container img {
    width: 100%; /* Ensure image fills the container width */
    height: 120%; /* Set height to 100% of the container */
    object-fit: cover; /* Ensure the entire image is visible without zooming */
    object-position: center; /* Keep the image centered */
}

/* Responsive Design for Mobile */
@media screen and (max-width: 768px) {
    .landing-page {
        flex-direction: column;
        height: auto; /* Allow the height to adjust on smaller screens */
    }

    .left-container, .right-container {
        width: 100%;
        padding: 20px;
        margin-bottom: 20px;
    }

    .left-container h2 {
        font-size: 28px;
    }

    .left-container p {
        font-size: 16px;
    }
}

/* Introduction Section */
.intro-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 150%; /* Same height as landing section */
    padding: 60px 30px;
    width: 100%;
}

.intro-left-container {
    width: 50%;
    padding: 20px;
}

.intro-left-container img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    margin-top: 10%;
}

.intro-right-container {
    width: 50%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-top: 8%;
}

.intro-right-container h2 {
    font-size: 40px;
    margin-bottom: 20px;
    margin-top: 0;
}

.intro-right-container p {
    font-size: 18px;
    line-height: 1.6;
    margin-top: 8%;
}

/* Responsive Design for Mobile */
@media screen and (max-width: 768px) {
    .landing-page, .intro-section {
        flex-direction: column;
        height: auto; /* Allow both sections to adjust their height on mobile */
    }

    .left-container, .right-container, .intro-left-container, .intro-right-container {
        width: 100%;
        margin-bottom: 20px;
    }

    .intro-right-container h2 {
        font-size: 28px;
    }

    .intro-right-container p {
        font-size: 16px;
    }
}


/* Services Section */
.services-section {
    background-color: #1B1D43;
    color: white;
    padding: 50px 20px;
    text-align: center;
    position: relative;
}

/* Section Title */
.section-title {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 50px;
    text-align: center;
}

/* Product Item Layout */
.product-item {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 50px;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    position: relative; /* Ensure it stays relative for z-index adjustments */
    margin-bottom: 10%;
}

/* Left Container for Product Image */
.product-left-container {
    width: 40%;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 20px; /* Keeps the image fixed when scrolling */
    z-index: 2; /* Ensures the image is above other content */
}

/* Product Image */
.product-left-container .product-image img {
    width: 70%;
    height: auto;
    border-radius: 300px;
    transition: opacity 1s ease-in-out, transform 1s ease-in-out;
}

/* Right Container for Product Content */
.product-right-container {
    width: 55%;
    padding: 20px;
    z-index: 1; /* Ensure the text is below the sticky image */
}

.product-right-container h3 {
    font-size: 28px;
    margin-bottom: 15px;
}

.product-right-container p {
    font-size: 18px;
    line-height: 1.6;
}

/* Fade-in effect when item is in view */
.product-item.active {
    opacity: 1;
}

/* Scroll-based Animation */
@media (max-width: 768px) {
    .product-item {
        flex-direction: column;
    }

    .product-left-container, .product-right-container {
        width: 100%;
    }

    .product-right-container h3 {
        font-size: 24px;
    }

    .product-right-container p {
        font-size: 16px;
    }
}


/* Benefits Section */
.benefits-section {
    background-color: #fff;
    color: #000000;
    padding: 50px 20px;
    text-align: center;
}


/* Benefits Cards Layout */
.benefits-cards {
    display: flex;
    justify-content: space-between;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 20px;
}

/* Individual Benefit Card */
.benefit-card {
    width: 23%;
    background-color: #1B1D43;
    padding: 30px;
    border-radius: 10px;
    position: relative;
    transition: all 0.3s ease-in-out;
    text-align: left;
}

.benefit-card:hover {
    transform: translateY(-10px); /* Slight lift on hover */
}

/* Card Icon */
.card-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
}

.card-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Card Text */
.benefit-card h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #fff;
}

.benefit-card p {
    font-size: 16px;
    line-height: 1.5;
    color: #fff;
}


/* Top Right and Bottom Left Border Effect */
.benefit-card::before, .benefit-card::after {
    content: "";
    position: absolute;
    width: 100px;
    height: 10px;
    background-color: #fff;
    transition: all 0.3s ease-in-out;
}

/* Top Right Corner */
.benefit-card::before {
    top: 10px;
    right: -20px;
    transform: rotate(45deg);
}

/* Bottom Left Corner */
.benefit-card::after {
    bottom: -10px;
    left: -20px;
    transform: rotate(45deg);
}

/* Responsive Design for Mobile */
@media screen and (max-width: 768px) {
    .benefits-cards {
        flex-direction: column;
        align-items: center;
    }

    .benefit-card {
        width: 80%;
        margin-bottom: 20px;
    }
}

footer {
    text-align: center;
    padding: 15px;
    font-size: 12px;
    background-color: #1B1D43;
    border-top: 1px solid #ccc;
    color: #fff;
}

footer a {
    color: #fff; /* Make the Privacy Policy link white */
    text-decoration: underline; /* Remove the underline */
}
