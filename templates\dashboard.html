<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RadioLens Dashboard</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>

<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('upload') }}">Upload</a></li>
                <li><a href="{{ url_for('report') }}">Report</a></li>
                <li><a href="{{ url_for('dashboard') }}" class="active">Dashboard</a></li>
                <li><a href="{{ url_for('history') }}">History</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>



        <div class="content">
            <div class="dashboard-section">
                <h2>Overview</h2>

                <!-- Top Section: Stats + Side Slicers -->
                <div class="top-section">
                    <!-- Left: Statistics Overview + Dropdown Slicers -->
                    <div class="left-section">
                        <div class="stats-overview">
                            <div class="stat-card">
                                <div class="stat-number" id="total-patients">0</div>
                                <div class="stat-label">Total Patients</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="total-patients-with-tumors">0</div>
                                <div class="stat-label">Total Patients with Tumors</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="total-healthy-patients">0</div>
                                <div class="stat-label">Total Healthy Patients</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="positive-cases-rate">0.00%</div>
                                <div class="stat-label">Positive Cases Rate</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="total-uploads">0</div>
                                <div class="stat-label">Total Images Uploaded</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="total-processed">0</div>
                                <div class="stat-label">Total Images Processed</div>
                            </div>
                        </div>

                        <!-- Dropdown Slicers moved down to align with right side slicers -->
                        <div class="dropdown-slicers" style="margin-top: 22px;">
                            <div class="dropdown-slicer">
                                <button class="slicer-clear" onclick="clearFilter('patientId')" title="Clear Patient ID Filter">✕</button>
                                <div class="dropdown-title">Patient ID</div>
                                <select class="dropdown-select" id="patient-id-filter">
                                    <option value="all">All Patients</option>
                                </select>
                            </div>

                            <div class="dropdown-slicer">
                                <button class="slicer-clear" onclick="clearFilter('scanDate')" title="Clear Scan Date Filter">✕</button>
                                <div class="dropdown-title">Scan Date</div>
                                <div class="date-range-container">
                                    <input type="date" class="date-input" id="scan-date-from" placeholder="From">
                                    <span class="date-separator">to</span>
                                    <input type="date" class="date-input" id="scan-date-to" placeholder="To">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right: Side Slicers -->
                    <div class="side-slicers">
                        <!-- Gender Slicer -->
                        <div class="stat-card slicer-card gender-slicer">
                            <button class="slicer-clear" onclick="clearFilter('gender')" title="Clear Gender Filter">✕</button>
                            <div class="slicer-title">Gender</div>
                            <div class="slicer-buttons" id="gender-slicer">
                                <button class="slicer-btn" data-filter="gender" data-value="all">All</button>
                                <button class="slicer-btn" data-filter="gender" data-value="female">Female</button>
                                <button class="slicer-btn" data-filter="gender" data-value="male">Male</button>
                            </div>
                        </div>

                        <!-- Tumor Class Slicer -->
                        <div class="stat-card slicer-card tumor-slicer">
                            <button class="slicer-clear" onclick="clearFilter('tumorType')" title="Clear Tumor Filter">✕</button>
                            <div class="slicer-title">Tumor Class</div>
                            <div class="slicer-buttons" id="tumor-slicer">
                                <button class="slicer-btn" data-filter="tumorType" data-value="all">All</button>
                                <button class="slicer-btn" data-filter="tumorType" data-value="glioma_tumor">Glioma Tumor</button>
                                <button class="slicer-btn" data-filter="tumorType" data-value="meningioma_tumor">Meningioma Tumor</button>
                                <button class="slicer-btn" data-filter="tumorType" data-value="pituitary_tumor">Pituitary Tumor</button>
                                <button class="slicer-btn" data-filter="tumorType" data-value="no_tumour">No Tumor</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Grid -->
                <div class="charts-grid">
                    <div class="chart-container">
                        <h3>Gender Distribution</h3>
                        <div class="chart-content" id="gender-chart">
                            <div class="loading">Loading...</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3>Tumor Types Distribution</h3>
                        <div class="chart-content" id="tumor-types-chart">
                            <div class="loading">Loading...</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3>Confidence Score Distribution</h3>
                        <div class="chart-content" id="confidence-chart">
                            <div class="loading">Loading...</div>
                        </div>
                    </div>

                    <div class="chart-container">
                        <h3>Age Group Distribution</h3>
                        <div class="chart-content" id="age-chart">
                            <div class="loading">Loading...</div>
                        </div>
                    </div>

                </div>

                <!-- Separate row for compact timeline chart -->
                <div class="timeline-section">
                    <div class="chart-container timeline-container">
                        <h3>Brain Tumor Cases Over Time</h3>
                        <div class="chart-content" id="timeline-chart">
                            <div class="loading">Loading...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>


    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
