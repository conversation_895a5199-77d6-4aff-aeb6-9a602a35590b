<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Raw Images - RadioLens</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/patient_raw_images.css') }}">
</head>

<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('upload') }}">Upload</a></li>
                <li><a href="{{ url_for('report') }}">Report</a></li>
                <li><a href="{{ url_for('dashboard') }}">Dashboard</a></li>
                <li><a href="{{ url_for('history') }}" class="active">History</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="header">
            <h1>Patient History</h1>
        </div>

        <div class="content">
            <button class="back-btn" onclick="window.location.href='/history'">← Back to History</button>
            
            <!-- Patient Information -->
            <div class="patient-info">
                <h2>Patient Information</h2>
                <div class="patient-details">
                    <div class="patient-detail">
                        <label>Patient ID</label>
                        <span>{{ patient.patient_id }}</span>
                    </div>
                    <div class="patient-detail">
                        <label>Patient Name</label>
                        <span>{{ patient.patient_name or 'Unknown' }}</span>
                    </div>
                    <div class="patient-detail">
                        <label>Gender</label>
                        <span>{{ patient.gender.title() if patient.gender else 'N/A' }}</span>
                    </div>
                    <div class="patient-detail">
                        <label>Age</label>
                        <span>{{ patient.age or 'N/A' }}</span>
                    </div>
                    <div class="patient-detail">
                        <label>Radiologist</label>
                        <span>{{ patient.radiologist or 'N/A' }}</span>
                    </div>
                    <div class="patient-detail">
                        <label>Total Images</label>
                        <span>{{ images|length }} images</span>
                    </div>
                </div>
            </div>

            <!-- Raw Images Table -->
            <div class="table-wrapper">
                <table class="images-table">
                    <thead>
                        <tr>
                            <th>Scan Date</th>
                            <th>Raw Image</th>
                            <th>Status</th>
                            <th>Diagnosis</th>
                            <th>Predicted Class</th>
                            <th>Confidence</th>
                            <th>Processed Image</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for image in images %}
                        <tr>
                            <td>
                                {% if image.upload_date %}
                                    {{ image.upload_date.rsplit(' ', 1)[0] if ' ' in image.upload_date else image.upload_date }}
                                {% endif %}
                            </td>
                            <td class="image-cell">
                                <img src="/raw_image/{{ image.filename }}"
                                     alt="Raw Image - {{ image.filename }}"
                                     class="table-image"
                                     onclick="showFullImage('/raw_image/{{ image.filename }}', 'Raw Image - {{ image.filename }}')"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="image-placeholder" style="display: none;">
                                    No Image
                                </div>
                            </td>
                            <td>
                                <span class="status-badge status-{{ image.status.lower() }}">
                                    {{ image.status }}
                                </span>
                            </td>
                            <td>
                                {% set diagnosis = 'Positive' if image.predicted_class and image.predicted_class.lower() not in ['no_tumour', 'no tumor'] else 'Negative' %}
                                <span class="diagnosis-badge diagnosis-{{ diagnosis.lower() }}">
                                    {{ diagnosis }}
                                </span>
                            </td>
                            <td>{{ image.predicted_class }}</td>
                            <td>{{ image.confidence }}</td>
                            <td class="image-cell">
                                {% if image.status == 'Analyzed' and image.processed_filename %}
                                    <img src="{{ url_for('serve_patient_image', patient_id=patient.patient_id, filename=image.processed_filename) }}"
                                         alt="Processed Image - {{ image.processed_filename }}"
                                         class="table-image clickable-image"
                                         data-image-url="{{ url_for('serve_patient_image', patient_id=patient.patient_id, filename=image.processed_filename) }}"
                                         data-image-title="Processed Image - {{ image.processed_filename }}"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="image-placeholder" style="display: none;">
                                        Image Load Error
                                    </div>
                                {% else %}
                                    <div class="image-placeholder">
                                        Not Processed
                                    </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Image Comparison Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Image Comparison</h2>
                <span class="close" onclick="closeImageModal()">&times;</span>
            </div>
            <div class="image-comparison">
                <div class="image-section">
                    <div class="image-title raw-image">Raw Image</div>
                    <div class="image-container">
                        <img id="rawImage" class="comparison-image" src="" alt="Raw Image" style="display: none;">
                        <div id="rawPlaceholder" class="image-placeholder">Loading raw image...</div>
                    </div>
                </div>
                <div class="image-section">
                    <div class="image-title processed-image">Processed Image</div>
                    <div class="image-container">
                        <img id="processedImage" class="comparison-image" src="" alt="Processed Image" style="display: none;">
                        <div id="processedPlaceholder" class="image-placeholder">No processed image available</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Full Image Modal -->
    <div id="fullImageModal" class="full-image-modal">
        <div class="full-image-content">
            <span class="full-image-close" onclick="closeFullImage()">&times;</span>
            <div class="full-image-title" id="fullImageTitle"></div>
            <img id="fullImageDisplay" class="full-image" src="" alt="">
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>


    <script src="{{ url_for('static', filename='js/patient_raw_images.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
