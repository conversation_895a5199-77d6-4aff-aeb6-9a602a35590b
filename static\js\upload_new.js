function triggerFolderUpload() {
    console.log('triggerFolderUpload called');
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.click();
    }
}

function calculateAge() {
    const dateOfBirthInput = document.getElementById('dateOfBirth');
    const ageInput = document.getElementById('age');

    if (dateOfBirthInput.value) {
        const dateValue = dateOfBirthInput.value.trim();

        // Check for incomplete or placeholder dates
        if (dateValue.length < 8 ||
            dateValue.includes('yyyy') ||
            dateValue.includes('mm') ||
            dateValue.includes('dd') ||
            dateValue.includes('_') ||
            dateValue === '' ||
            dateValue.split(/[-\/]/).some(part => part === '' || part.length < 2)) {
            // Date is incomplete or contains placeholders, don't calculate age yet
            ageInput.value = '';
            return;
        }

        const birthDate = new Date(dateValue);
        const today = new Date();

        // Check if the parsed date is valid
        if (isNaN(birthDate.getTime()) || birthDate.getFullYear() < 1800) {
            // Invalid date, don't show error popup, just clear age
            ageInput.value = '';
            return;
        }

        // Check if birth date is in the future
        if (birthDate > today) {
            alert('Date of birth cannot be in the future!');
            dateOfBirthInput.value = '';
            ageInput.value = '';
            return;
        }

        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();

        // Adjust age if birthday hasn't occurred this year yet
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }

        // Check for reasonable age limits - but only if we have a reasonable birth year
        if (age < 0) {
            age = 0;
        } else if (age > 150) {
            // Only show alert if the date seems intentionally complete
            const birthYear = birthDate.getFullYear();
            if (birthYear > 1800 && birthYear < 2100) {
                alert('Please check the date of birth. Age cannot exceed 150 years.');
                dateOfBirthInput.value = '';
                ageInput.value = '';
                return;
            } else {
                // Probably an incomplete/invalid date, just clear age
                ageInput.value = '';
                return;
            }
        }

        // Set the calculated age
        ageInput.value = age;

        console.log('Age calculated:', age, 'from birth date:', dateOfBirthInput.value);

        // Trigger form completion check
        checkFormCompletion();
    } else {
        ageInput.value = '';
    }
}

function handleRadiologistInput(event) {
    const input = event.target;
    const prefix = 'Dr. ';

    // If the value doesn't start with "Dr. ", add it
    if (!input.value.startsWith(prefix)) {
        input.value = prefix + input.value.replace(/^Dr\.\s*/, '');
    }

    // Prevent cursor from going before "Dr. "
    if (input.selectionStart < prefix.length) {
        input.setSelectionRange(prefix.length, prefix.length);
    }
}

function handleRadiologistKeydown(event) {
    const input = event.target;
    const prefix = 'Dr. ';

    // Prevent deletion of "Dr. " prefix
    if (input.selectionStart <= prefix.length &&
        (event.key === 'Backspace' || event.key === 'Delete' ||
            event.key === 'ArrowLeft' || event.key === 'Home')) {

        if (event.key === 'Backspace' || event.key === 'Delete') {
            event.preventDefault();
        } else if (event.key === 'ArrowLeft' || event.key === 'Home') {
            event.preventDefault();
            input.setSelectionRange(prefix.length, prefix.length);
        }
    }
}

function handleRadiologistFocus(event) {
    const input = event.target;
    const prefix = 'Dr. ';

    // Position cursor after "Dr. " when focused
    setTimeout(() => {
        if (input.selectionStart < prefix.length) {
            input.setSelectionRange(prefix.length, prefix.length);
        }
    }, 0);
}

function handleFileChange(event) {
    console.log('handleFileChange called');
    event.preventDefault();

    const fileList = event.target.files;
    console.log('Files selected:', fileList.length);

    if (fileList.length > 0) {
        window.selectedFiles = fileList;

        // Extract folder name from the first file's path
        const firstFile = fileList[0];
        const pathParts = firstFile.webkitRelativePath.split('/');
        window.selectedFolderName = pathParts[0]; // Get the root folder name

        console.log('Selected folder name:', window.selectedFolderName);

        displaySelectedFiles(fileList);
        checkFormCompletion();
        console.log('Files stored for later upload');
    }

    return false;
}

function displaySelectedFiles(fileList) {
    const uploadBox = document.querySelector('.upload-box');
    let totalSize = 0;

    Array.from(fileList).forEach(file => {
        totalSize += file.size;
    });

    uploadBox.innerHTML = `
        <div class="upload-icon">✅</div>
        <div class="upload-text">
            <div class="upload-title">Folder selected: ${fileList.length} files</div>
            <div class="upload-subtitle">Total size: ${formatFileSize(totalSize)}</div>
        </div>
        <button type="button" class="clear-folder-btn" onclick="clearSelection()">Clear Folder</button>
    `;

    // Show image previews in a styled container only after folder selection
    const previewContainer = document.getElementById('image-preview-container');
    previewContainer.innerHTML = '';
    const imageFiles = Array.from(fileList).filter(file => file.type.startsWith('image/'));
    if (imageFiles.length === 0) {
        previewContainer.innerHTML = '<div class="no-images-msg">No images found in selected folder.</div>';
        return;
    }
    // Create the outer box
    const galleryBox = document.createElement('div');
    galleryBox.className = 'image-gallery-box';
    galleryBox.title = 'Click to view all images';
    // Add images inside the box
    imageFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'patient-preview-img';
            galleryBox.appendChild(img);
        };
        reader.readAsDataURL(file);
    });
    // When all images are loaded, append the box
    setTimeout(() => {
        previewContainer.appendChild(galleryBox);
    }, 100);
    // Add click event to open gallery modal
    galleryBox.onclick = function() {
        openGalleryModal(imageFiles);
    };

// Show all images in a modal gallery
function openGalleryModal(imageFiles) {
    const modal = document.getElementById('image-modal');
    const modalContent = document.getElementById('image-modal-gallery');
    modalContent.innerHTML = '';
    imageFiles.forEach(file => {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'gallery-modal-img';
            modalContent.appendChild(img);
        };
        reader.readAsDataURL(file);
    });
    modal.style.display = 'flex';
}

// Ensure modal close logic is always attached
function setupImageModalClose() {
    const modal = document.getElementById('image-modal');
    const closeBtn = document.getElementById('image-modal-close');
    if (closeBtn) {
        closeBtn.onclick = function(e) {
            e.stopPropagation();
            modal.style.display = 'none';
        };
    }
    if (modal) {
        modal.onclick = function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        };
    }
}

// Attach modal close logic after DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupImageModalClose);
} else {
    setupImageModalClose();
}
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function clearSelection() {
    window.selectedFiles = null;
    const uploadBox = document.querySelector('.upload-box');
    uploadBox.innerHTML = `
        <div class="upload-icon">📁</div>
        <div class="upload-text">
            <div class="upload-title">Upload MRI folder containing images</div>
            <div class="upload-subtitle">Select entire folder with medical images</div>
        </div>
        <div class="info-icon">ℹ️</div>
    `;
    // Also clear image preview and modal
    const previewContainer = document.getElementById('image-preview-container');
    if (previewContainer) previewContainer.innerHTML = '';
    const modal = document.getElementById('image-modal');
    if (modal) modal.style.display = 'none';
    checkFormCompletion();
}

function checkFormCompletion() {
    const form = document.getElementById('patientForm');
    const formData = new FormData(form);
    const saveBtn = document.querySelector('.save-btn');

    const requiredFields = ['patientId', 'patientName', 'gender', 'age', 'dateOfBirth', 'radiologist'];
    const allFieldsFilled = requiredFields.every(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            return false;
        }

        // Special validation for radiologist field - must have more than just "Dr. "
        if (field === 'radiologist') {
            const trimmedValue = value.trim();
            return trimmedValue.length > 4 && trimmedValue !== 'Dr.' && !trimmedValue.match(/^Dr\.?\s*$/);
        }

        return true;
    });

    const filesSelected = window.selectedFiles && window.selectedFiles.length > 0;

    // Patient ID verification with folder name
    let patientIdMatches = true;
    if (allFieldsFilled && filesSelected) {
        const patientId = formData.get('patientId').trim();
        const folderName = window.selectedFolderName;
        patientIdMatches = patientId === folderName;

        console.log('Patient ID:', patientId);
        console.log('Folder Name:', folderName);
        console.log('ID Matches:', patientIdMatches);
    }

    // Reset button styles
    saveBtn.style.backgroundColor = '';
    saveBtn.style.color = '';

    // Update verification status indicator
    let statusIndicator = document.getElementById('verification-status');
    if (!statusIndicator) {
        statusIndicator = document.createElement('div');
        statusIndicator.id = 'verification-status';
        statusIndicator.style.marginTop = '18px';
        statusIndicator.style.fontSize = '14px';
        statusIndicator.style.fontWeight = 'bold';
        statusIndicator.style.textAlign = 'center';
        // Insert after upload box, before image preview container
        const uploadBox = document.querySelector('.upload-box');
        const previewContainer = document.getElementById('image-preview-container');
        if (uploadBox && previewContainer) {
            uploadBox.parentNode.insertBefore(statusIndicator, previewContainer);
        } else {
            // fallback
            saveBtn.parentNode.insertBefore(statusIndicator, saveBtn);
        }
    }

    if (allFieldsFilled && filesSelected && patientIdMatches) {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save Patient & Upload Folder';
        saveBtn.classList.remove('disabled');
        saveBtn.style.backgroundColor = '#28a745'; // Green color
        saveBtn.style.color = 'white';
        statusIndicator.innerHTML = '✅ Patient ID matches folder name';
        statusIndicator.style.color = '#28a745';
    } else if (allFieldsFilled && filesSelected && !patientIdMatches) {
        saveBtn.disabled = true;
        saveBtn.textContent = 'Verification Failed';
        saveBtn.classList.add('disabled');
        saveBtn.style.backgroundColor = '#dc3545'; // Red color
        saveBtn.style.color = 'white';
        statusIndicator.innerHTML = `❌ Patient ID "${formData.get('patientId').trim()}" ≠ Folder "${window.selectedFolderName}"`;
        statusIndicator.style.color = '#dc3545';
    } else {
        saveBtn.disabled = true;
        if (!allFieldsFilled && !filesSelected) {
            saveBtn.textContent = 'Fill Patient Info & Select Folder';
        } else if (!allFieldsFilled) {
            saveBtn.textContent = 'Fill Patient Information';
        } else if (!filesSelected) {
            saveBtn.textContent = 'Select MRI Folder';
        }
        saveBtn.classList.add('disabled');
        statusIndicator.innerHTML = '';
    }
}

function savePatientAndFiles() {
    console.log('savePatientAndFiles called');

    if (!window.selectedFiles || window.selectedFiles.length === 0) {
        alert('Please select a folder with MRI images first.');
        return;
    }

    const form = document.getElementById('patientForm');
    const formData = new FormData(form);

    const requiredFields = ['patientId', 'patientName', 'gender', 'age', 'dateOfBirth', 'radiologist'];
    const missingFields = requiredFields.filter(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            return true;
        }

        // Special validation for radiologist field - must have more than just "Dr. "
        if (field === 'radiologist') {
            const trimmedValue = value.trim();
            return trimmedValue.length <= 4 || trimmedValue === 'Dr.' || trimmedValue.match(/^Dr\.?\s*$/);
        }

        return false;
    });

    if (missingFields.length > 0) {
        // Special message for radiologist field
        if (missingFields.includes('radiologist')) {
            const radiologistValue = formData.get('radiologist');
            if (radiologistValue && radiologistValue.trim().match(/^Dr\.?\s*$/)) {
                alert('Please enter the full name of the radiologist after "Dr. "');
                return;
            }
        }
        alert(`Please fill in the following required fields: ${missingFields.join(', ')}`);
        return;
    }

    // Verify Patient ID matches folder name
    const patientId = formData.get('patientId').trim();
    const folderName = window.selectedFolderName;
    if (patientId !== folderName) {
        alert(`❌ VERIFICATION FAILED!\n\nPatient ID: "${patientId}"\nFolder Name: "${folderName}"\n\nThe Patient ID must match the folder name exactly.\nPlease check your Patient ID or select the correct folder.`);
        return;
    }

    const saveBtn = document.querySelector('.save-btn');
    saveBtn.disabled = true;
    saveBtn.textContent = '🔄 Saving Patient & Running YOLO Analysis...';

    const flaskFormData = new FormData();
    flaskFormData.append('patient_id', formData.get('patientId'));
    flaskFormData.append('patient_name', formData.get('patientName'));
    flaskFormData.append('gender', formData.get('gender'));
    flaskFormData.append('age', formData.get('age'));
    flaskFormData.append('date_of_birth', formData.get('dateOfBirth'));
    flaskFormData.append('radiologist', formData.get('radiologist'));

    for (let i = 0; i < window.selectedFiles.length; i++) {
        flaskFormData.append('files[]', window.selectedFiles[i]);
    }

    fetch('/upload', {
        method: 'POST',
        body: flaskFormData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            let message = '✅ SUCCESS! Patient & Files Saved with YOLO Analysis!\n\n';
            message += `👤 Patient: ${data.patient_name} (${data.patient_id})\n`;
            message += `📁 Files Uploaded: ${data.files_uploaded}\n`;
            
            if (data.analysis_results && data.analysis_results.length > 0) {
                message += '\n🧠 YOLO ANALYSIS RESULTS:\n';
                message += '═══════════════════════════════\n';
                data.analysis_results.forEach((result, index) => {
                    const confidence = (result.confidence * 100).toFixed(1);
                    message += `${index + 1}. ${result.original_image}\n`;
                    message += `   🔍 Prediction: ${result.predicted_class}\n`;
                    message += `   📊 Confidence: ${confidence}%\n`;
                    if (result.mask_area > 0) {
                        message += `   📐 Detection Area: ${result.mask_area} pixels\n`;
                        message += `   📏 Bounding Box: ${result.width}×${result.height}\n`;
                    }
                    message += `   ⏱️ Processing Time: ${result.processing_time}s\n\n`;
                });
            }
            
            message += '💾 DATABASE STORAGE:\n';
            message += '═══════════════════════════════\n';
            message += `✅ patients table: Patient info saved\n`;
            message += `✅ uploads table: ${data.files_uploaded} files saved\n`;
            message += `✅ images_result table: ${data.analysis_completed} analysis results saved\n`;
            
            alert(message);
            
            form.reset();
            clearSelection();
        } else {
            alert('Error: ' + (data.message || 'Unknown error occurred'));
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        alert('Upload failed. Please check your connection and try again.');
    })
    .finally(() => {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save Patient & Upload Folder';
        checkFormCompletion();
    });
}

// Function to fetch and populate the next patient ID
function loadNextPatientId() {
    console.log('Fetching next patient ID...');

    fetch('/get_next_patient_id')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const patientIdInput = document.getElementById('patientId');
                if (patientIdInput) {
                    patientIdInput.value = data.next_patient_id;
                    console.log('✅ Auto-populated Patient ID:', data.next_patient_id);

                    // Add a visual indicator that this was auto-generated
                    patientIdInput.style.backgroundColor = '#e8f5e8';
                    patientIdInput.title = `Auto-generated based on last patient in database`;

                    // Trigger form completion check
                    checkFormCompletion();
                }
            } else {
                console.error('❌ Failed to get next patient ID:', data.message);
                // Fallback to P0001 if there's an error
                const patientIdInput = document.getElementById('patientId');
                if (patientIdInput) {
                    patientIdInput.value = 'P0001';
                    patientIdInput.style.backgroundColor = '#fff3cd';
                    patientIdInput.title = 'Fallback ID - please verify';
                }
            }
        })
        .catch(error => {
            console.error('❌ Error fetching next patient ID:', error);
            // Fallback to P0001 if there's an error
            const patientIdInput = document.getElementById('patientId');
            if (patientIdInput) {
                patientIdInput.value = 'P0001';
                patientIdInput.style.backgroundColor = '#fff3cd';
                patientIdInput.title = 'Fallback ID - please verify';
            }
        });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up event listeners');

    // Load the next patient ID automatically
    loadNextPatientId();

    const formFields = document.querySelectorAll('#patientForm input, #patientForm select');
    formFields.forEach(field => {
        field.addEventListener('change', checkFormCompletion);
        field.addEventListener('input', checkFormCompletion);
    });

    // Add specific event listener for date of birth
    const dateOfBirthInput = document.getElementById('dateOfBirth');
    if (dateOfBirthInput) {
        dateOfBirthInput.addEventListener('change', calculateAge);
    }

    // Add specific event listeners for radiologist field
    const radiologistInput = document.getElementById('radiologist');
    if (radiologistInput) {
        radiologistInput.addEventListener('input', handleRadiologistInput);
        radiologistInput.addEventListener('keydown', handleRadiologistKeydown);
        radiologistInput.addEventListener('focus', handleRadiologistFocus);

        // Set initial cursor position
        setTimeout(() => {
            radiologistInput.setSelectionRange(4, 4); // Position after "Dr. "
        }, 0);
    }

    checkFormCompletion();
});


// Helper to collect form data and files, send via AJAX, and redirect on success
function savePatientAndFiles() {
    const form = document.getElementById('patientForm');
    const patientId = document.getElementById('patientId').value.trim();
    const patientName = document.getElementById('patientName').value.trim();
    const gender = document.getElementById('gender').value;
    const dateOfBirth = document.getElementById('dateOfBirth').value;
    const age = document.getElementById('age').value;
    const radiologist = document.getElementById('radiologist').value.trim();
    const fileInput = document.getElementById('fileInput');
    if (!patientId || !patientName || !gender || !dateOfBirth || !age || !radiologist || !fileInput.files.length) {
        alert('Please fill all patient info and select at least one image folder.');
        return;
    }
    const formData = new FormData();
    formData.append('patient_id', patientId);
    formData.append('patient_name', patientName);
    formData.append('gender', gender);
    formData.append('date_of_birth', dateOfBirth);
    formData.append('age', age);
    formData.append('radiologist', radiologist);
    for (let i = 0; i < fileInput.files.length; i++) {
        formData.append('files[]', fileInput.files[i]);
    }
    // Show loading indicator (optional)
    document.getElementById('saveBtn').textContent = '🔄 Saving Patient & Running YOLO Analysis...';
    document.getElementById('saveBtn').disabled = true;
    fetch('/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.patient_id) {
            // Redirect to the report page for this patient
            window.location.href = `/patient/${data.patient_id}/report`;
        } else {
            alert(data.message || 'Upload failed.');
            document.getElementById('saveBtn').textContent = 'Fill Patient Info & Select Folder';
            document.getElementById('saveBtn').disabled = false;
        }
    })
    .catch(err => {
        alert('An error occurred during upload.');
        document.getElementById('saveBtn').textContent = 'Fill Patient Info & Select Folder';
        document.getElementById('saveBtn').disabled = false;
    });
}