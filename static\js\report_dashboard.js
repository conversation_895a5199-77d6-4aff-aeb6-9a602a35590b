// Toggle confidence scores expand/collapse
function toggleConfidenceExpand(button) {
    const container = button.closest('.confidence-scores-container');
    const grid = container.querySelector('.confidence-grid');
    const expandText = button.querySelector('.expand-text');

    if (grid.style.maxHeight === 'none' || grid.style.maxHeight === '') {
        grid.style.maxHeight = '120px';
        expandText.textContent = 'Show All';
        button.style.background = '#f8f9fa';
        button.style.color = '#667eea';
    } else {
        grid.style.maxHeight = 'none';
        expandText.textContent = 'Show Less';
        button.style.background = '#667eea';
        button.style.color = 'white';
    }
}

// Add some interactivity
document.addEventListener('DOMContentLoaded', function() {
    // Animate confidence bars on load
    const confidenceBars = document.querySelectorAll('.confidence-fill-compact');
    confidenceBars.forEach((bar, index) => {
        setTimeout(() => {
            const originalWidth = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = originalWidth;
            }, 50);
        }, index * 100);
    });

    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add click handlers for table rows
    const tableRows = document.querySelectorAll('.reports-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function(e) {
            if (!e.target.closest('.action-buttons')) {
                const viewButton = this.querySelector('.btn-outline');
                if (viewButton) {
                    window.location.href = viewButton.href;
                }
            }
        });
    });

    // Add tooltip functionality for confidence cards
    const confidenceCards = document.querySelectorAll('.confidence-card');
    confidenceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            const percentage = this.querySelector('.confidence-percentage').textContent;
            const tumorName = this.querySelector('.confidence-tumor-label').textContent;
            this.title = `${tumorName}: ${percentage} confidence`;
        });
    });
});

// Filter Functions
function applyReportFilters() {
    const scanDate = document.getElementById('scan-date').value;
    const patientId = document.getElementById('patient-id').value.toLowerCase();
    const patientName = document.getElementById('patient-name').value.toLowerCase();
    const tumorType = document.getElementById('tumor-type').value.toLowerCase();

    const tableRows = document.querySelectorAll('.reports-table tbody tr');

    tableRows.forEach(row => {
        let showRow = true;

        // Filter by scan date
        if (scanDate) {
            const rowDate = row.querySelector('td:nth-child(3)').textContent.trim();
            if (!rowDate.includes(scanDate)) {
                showRow = false;
            }
        }

        // Filter by patient ID
        if (patientId) {
            const rowPatientId = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
            if (!rowPatientId.includes(patientId)) {
                showRow = false;
            }
        }

        // Filter by patient name
        if (patientName) {
            const rowPatientName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            if (!rowPatientName.includes(patientName)) {
                showRow = false;
            }
        }

        // Filter by tumor type
        if (tumorType) {
            const rowTumorType = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
            if (tumorType === 'no_tumor') {
                if (!rowTumorType.includes('no tumor') && !rowTumorType.includes('negative')) {
                    showRow = false;
                }
            } else {
                if (!rowTumorType.includes(tumorType)) {
                    showRow = false;
                }
            }
        }

        // Show/hide row
        row.style.display = showRow ? '' : 'none';
    });
}

function clearReportFilters() {
    // Clear all filter inputs
    document.getElementById('scan-date').value = '';
    document.getElementById('patient-id').value = '';
    document.getElementById('patient-name').value = '';
    document.getElementById('tumor-type').value = '';

    // Show all rows
    const tableRows = document.querySelectorAll('.reports-table tbody tr');
    tableRows.forEach(row => {
        row.style.display = '';
    });
}