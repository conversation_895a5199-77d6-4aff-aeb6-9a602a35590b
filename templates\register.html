<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RadioLens Registration</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/register.css') }}">
</head>

<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="#" onclick="alert('Please register or login first')">Register</a></li>
                <li><a href="{{ url_for('about') }}">About Us</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="header">
            <h1>Join RadioLens</h1>
        </div>

        <div class="content">
            <div class="register-section">
                <h2>Create Your Account</h2>
                <p class="register-subtitle">Join our medical imaging platform and start your journey</p>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form id="registerForm" method="post" action="{{ url_for('register') }}">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="first_name">First Name:</label>
                            <input type="text" id="first_name" name="first_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="last_name">Last Name:</label>
                            <input type="text" id="last_name" name="last_name" required>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="email">Email Address:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="user_id">User ID:</label>
                        <input type="text" id="user_id" name="user_id" required>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="password">Password:</label>
                        <div class="password-container">
                            <input type="password" id="password" name="password" required>
                            <span id="togglePassword" class="password-toggle">Show</span>
                        </div>





                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="register-btn">Create Account</button>
                    </div>
                    
                    <div class="login-section">
                        <p>Already have an account? <a href="{{ url_for('login') }}" class="login-link">Log In</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>


    <script src="{{ url_for('static', filename='js/register.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
