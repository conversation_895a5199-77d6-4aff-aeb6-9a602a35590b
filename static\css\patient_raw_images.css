* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

header nav ul li a.active {
    font-weight: bold;
    color: #1B1D43;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 95%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.content {
    background: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
    min-height: 600px;
    padding: 40px;
}

.patient-info {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    color: #1B1D43;
}

.patient-info h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1B1D43;
    margin-bottom: 15px;
}

.patient-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.patient-detail {
    display: flex;
    flex-direction: column;
}

.patient-detail label {
    font-weight: 600;
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.patient-detail span {
    font-size: 16px;
    color: #1B1D43;
    font-weight: 500;
}

.back-btn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    margin-bottom: 20px;
}

.back-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

/* Table Styles */
.table-wrapper {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.images-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    table-layout: fixed;
}

.images-table th {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.images-table th:nth-child(1) { width: 12%; } /* Scan Date */
.images-table th:nth-child(2) { width: 20%; } /* Raw Image */
.images-table th:nth-child(3) { width: 13%; } /* Status */
.images-table th:nth-child(4) { width: 11%; } /* Diagnosis */
.images-table th:nth-child(5) { width: 15%; } /* Predicted Class */
.images-table th:nth-child(6) { width: 10%; } /* Confidence */
.images-table th:nth-child(7) { width: 22%; } /* Processed Image */

.images-table td {
    padding: 15px;
    border-bottom: 1px solid #e1e5e9;
    color: #333;
    font-size: 14px;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.images-table td:nth-child(2) {
    word-break: break-all;
    max-width: 0;
}

.images-table tr:hover {
    background-color: #f8fafc;
}

.images-table tr:last-child td {
    border-bottom: none;
}

/* Action buttons styling */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
}

.btn {
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 10px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    width: 100%;
    text-align: center;
}

.btn-outline {
    background: transparent;
    border: 1px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Status and Diagnosis Badges */
.status-badge, .diagnosis-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-uploaded {
    background: #e3f2fd;
    color: #1976d2;
}

.status-analyzed {
    background: #e8f5e8;
    color: #2e7d32;
}

.diagnosis-positive {
    background: #ffebee;
    color: #c62828;
}

.diagnosis-negative {
    background: #e8f5e8;
    color: #2e7d32;
}

.diagnosis-pending {
    background: #fff3e0;
    color: #f57c00;
}

/* Image thumbnails in table */
.table-image {
    max-width: 160px;
    max-height: 220px;
    width: auto;
    height: auto;
    border-radius: 6px;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s ease;
}

.table-image:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.image-cell {
    text-align: center;
    padding: 15px;
}

.image-placeholder {
    display: inline-block;
    width: 120px;
    height: 90px;
    background: #f8fafc;
    border: 2px dashed #e1e5e9;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 12px;
    text-align: center;
}

/* Full Image Modal */
.full-image-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.full-image-content {
    position: relative;
    margin: auto;
    padding: 20px;
    width: 90%;
    height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.full-image {
    max-width: 100%;
    max-height: 80%;
    width: auto;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
}

.full-image-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    text-align: center;
}

.full-image-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.full-image-close:hover {
    color: #ff6b6b;
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    width: 103.9%;
    margin: 0;
    margin-top: 50px;
    position: relative;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}

/* Image Comparison Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 85%;
    max-width: 900px;
    max-height: 80vh;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e1e5e9;
}

.modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #1B1D43;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #000;
}

.image-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

.image-section {
    text-align: center;
}

.image-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 8px;
}

.image-title.raw-image {
    color: #4a5568;
    background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
    border-left: 4px solid #a0aec0;
}

.image-title.processed-image {
    color: #ffffff;
    background: linear-gradient(135deg, #526c9a 0%, #6e7a91 100%);
    border-left: 4px solid #4299e1;
}

.image-container {
    background: #f8fafc;
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    min-height: 250px;
    max-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.comparison-image {
    max-width: 100%;
    max-height: 280px;
    width: auto;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    object-fit: contain;
}

.image-placeholder {
    color: #666;
    font-style: italic;
    font-size: 16px;
}

.btn-compare {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
}

.btn-compare:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 8px;
        flex-wrap: wrap;
    }

    header .logo img {
        width: 50px;
        margin-left: 15px;
        max-height: 40px;
    }

    header nav ul {
        flex-wrap: wrap;
        gap: 10px;
    }

    header nav ul li {
        margin-right: 15px;
    }

    header nav ul li a {
        font-size: 14px;
    }

    body {
        padding: 120px 20px 0 20px;
    }

    .container {
        width: 95%;
    }

    .header h1 {
        font-size: 28px;
    }

    .content {
        padding: 30px 20px;
    }

    .patient-details {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .patient-detail {
        padding: 12px;
    }

    .patient-detail label {
        font-size: 12px;
    }

    .patient-detail span {
        font-size: 14px;
    }

    .table-wrapper {
        padding: 15px;
        overflow-x: auto;
    }

    .images-table {
        min-width: 900px;
    }

    .images-table th,
    .images-table td {
        padding: 10px 8px;
        font-size: 12px;
    }

    .table-image {
        max-width: 90px;
        max-height: 70px;
    }

    .image-placeholder {
        width: 90px;
        height: 70px;
        font-size: 10px;
    }

    .action-buttons {
        min-width: 120px;
    }

    .btn-compare {
        font-size: 10px;
        padding: 4px 8px;
    }

    .back-btn {
        font-size: 14px;
        padding: 10px 20px;
        margin-bottom: 20px;
    }

    .image-comparison {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-content {
        width: 98%;
        margin: 1% auto;
        padding: 15px;
    }

    .modal-title {
        font-size: 20px;
    }

    .image-container {
        min-height: 200px;
        max-height: 250px;
    }

    .comparison-image {
        max-height: 230px;
    }

    footer {
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        padding: 12px 20px;
        font-size: 11px;
        width: 100vw;
    }
}