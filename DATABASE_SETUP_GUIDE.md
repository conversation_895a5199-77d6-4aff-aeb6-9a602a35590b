# 🗄️ Radiolens Database Setup Guide for phpMyAdmin

## 📋 Prerequisites

1. **XAMPP installed** and running
2. **MySQL service** started in XAMPP Control Panel
3. **phpMyAdmin** accessible at `http://localhost/phpmyadmin`

## 🚀 Quick Setup Steps

### Method 1: Automatic Setup (Recommended)
1. **Start XAMPP** and ensure MySQL is running
2. **Run your Flask app**: `python app.py`
3. The app will **automatically**:
   - Test database connection
   - Create database `patient_info`
   - Create tables `patients` and `images_result`
   - Display connection status

### Method 2: Manual Setup via phpMyAdmin
1. **Open phpMyAdmin**: `http://localhost/phpmyadmin`
2. **Click "SQL" tab**
3. **Copy and paste** the contents of `setup_database.sql`
4. **Click "Go"** to execute

## 🔧 Database Configuration

Your `app.py` is configured with:

```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '',  # Default XAMPP password
    'database': 'patient_info',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}
```

## 📊 Database Structure

### `patients` Table
- **patient_id** (VARCHAR(50), PRIMARY KEY)
- **patient_name** (VARCHAR(100), NOT NULL)
- **gender** (ENUM: 'male', 'female', 'other')
- **age** (INT, NOT NULL)
- **date_of_birth** (DATE, NOT NULL)
- **radiologist** (VARCHAR(100), NOT NULL)
- **created_at** (TIMESTAMP)
- **updated_at** (TIMESTAMP)

### `images_result` Table
- **id** (INT, AUTO_INCREMENT, PRIMARY KEY)
- **patient_id** (VARCHAR(50), FOREIGN KEY)
- **patient_name** (VARCHAR(100))
- **result_image** (VARCHAR(255))
- **predicted_class** (VARCHAR(100))
- **confidence** (DECIMAL(5,4))
- **x1, y1, x2, y2** (INT) - Bounding box coordinates
- **width, height** (INT) - Image dimensions
- **mask_area** (INT)
- **location** (VARCHAR(255))
- **created_at, updated_at** (TIMESTAMP)

## 🔍 Verification Steps

1. **Check phpMyAdmin**:
   - Go to `http://localhost/phpmyadmin`
   - Look for `patient_info` database
   - Verify tables exist: `patients`, `images_result`

2. **Test Flask Connection**:
   - Run `python app.py`
   - Look for success messages:
     ```
     ✅ Successfully connected to MySQL Server
     ✅ Database 'patient_info' created or already exists
     ✅ 'patients' table created successfully
     ✅ 'images_result' table created successfully
     ```

## 🚨 Troubleshooting

### Connection Failed?
1. **Check XAMPP**: Ensure MySQL is running (green light)
2. **Check Port**: Default is 3306, verify in XAMPP config
3. **Check Password**: Default XAMPP password is empty (`''`)

### Tables Not Created?
1. **Check Permissions**: Ensure MySQL user has CREATE privileges
2. **Check Database**: Verify `patient_info` database exists
3. **Manual Creation**: Use the SQL script in phpMyAdmin

### Foreign Key Errors?
1. **Create patients table first** (referenced by images_result)
2. **Check InnoDB engine** is enabled
3. **Verify patient_id** exists before inserting image results

## 🎯 Success Indicators

When everything is working correctly, you should see:

```
🔄 Attempting to connect to MySQL database...
✅ Successfully connected to MySQL Server version 8.0.x
📊 Connected to database: patient_info
✅ Database 'patient_info' created or already exists
✅ 'patients' table created successfully
✅ 'images_result' table created successfully
✅ Database initialization completed successfully!
🌐 You can now view your data in phpMyAdmin at: http://localhost/phpmyadmin
```

## 📱 Usage

Once set up, your Flask app will:
1. **Save patient info** to `patients` table when uploading
2. **Save analysis results** to `images_result` table after processing
3. **Display data** in phpMyAdmin for easy viewing and management

## 🔗 Quick Links

- **Flask App**: `http://127.0.0.1:5000`
- **phpMyAdmin**: `http://localhost/phpmyadmin`
- **Database**: `patient_info`
- **Upload Page**: `http://127.0.0.1:5000/upload`
