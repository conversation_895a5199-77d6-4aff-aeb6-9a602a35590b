<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Upload MRI Scan</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/upload_new.css') }}">
</head>


<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('upload') }}">Upload</a></li>
                <li><a href="{{ url_for('report') }}">Report</a></li>
                <li><a href="{{ url_for('dashboard') }}" class="active">Dashboard</a></li>
                <li><a href="{{ url_for('history') }}">History</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="header">
            <h1>Upload MRI Scan</h1>
        </div>

        <div class="content">
            <div class="left-section">
                <h2>Patient Information</h2>
                <form id="patientForm" enctype="multipart/form-data" method="POST" onsubmit="return false;">
                    <div class="form-group">
                        <label for="patientId">Patient ID:</label>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="text" id="patientId" name="patientId" required style="flex: 1;">
                            <button type="button" onclick="loadNextPatientId()"
                                    style="background: #007BFF; color: white; border: none; border-radius: 6px; padding: 12px 16px; cursor: pointer; font-size: 14px; transition: all 0.3s ease;"
                                    onmouseover="this.style.background='#0056b3'"
                                    onmouseout="this.style.background='#007BFF'"
                                    title="Refresh to get next available Patient ID">
                                🔄
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="patientName">Patient Name:</label>
                        <input type="text" id="patientName" name="patientName" required>
                    </div>

                    <div class="form-group">
                        <label for="gender">Gender:</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="dateOfBirth">Date of Birth:</label>
                        <input type="date" id="dateOfBirth" name="dateOfBirth" required onchange="calculateAge()">
                    </div>

                    <div class="form-group">
                        <label for="age">Age:</label>
                        <input type="number" id="age" name="age" min="1" max="150" required readonly>
                    </div>

                    <div class="form-group">
                        <label for="radiologist">Radiologist</label>
                        <input type="text" id="radiologist" name="radiologist" value="Dr. " required>
                    </div>
                </form>
            </div>

            <div class="right-section">
                <h2>Upload MRI Images</h2>
                <div class="upload-box" onclick="triggerFolderUpload()">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <div class="upload-title">Upload MRI folder containing images</div>
                        <div class="upload-subtitle">Select entire folder with medical images</div>
                    </div>
                    <div class="info-icon">ℹ️</div>
                </div>

                <!-- Verification status indicator will be inserted here by JS -->
                <div id="verification-status" style="margin-top: 18px;"></div>

                <!-- Image preview container (populated by JS) -->
                <div id="image-preview-container"></div>

                <!-- Modal for full image preview -->
                <div id="image-modal" class="image-modal" style="display:none;">
                    <span class="image-modal-close" id="image-modal-close">&times;</span>
                    <div id="image-modal-gallery" class="image-modal-gallery"></div>
                </div>

                <!-- Save Button moved here under upload box -->
                <button type="button" class="save-btn disabled" id="saveBtn" onclick="savePatientAndFiles()" disabled>
                    Fill Patient Info & Select Folder
                </button>

                <input type="file" id="fileInput" webkitdirectory directory multiple style="display: none;" onchange="handleFileChange(event)">
            </div>
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>

    <script src="{{ url_for('static', filename='js/upload_new.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
