* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

header nav ul li a.active {
    font-weight: bold;
    color: #1B1D43;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 93%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.content {
    background: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
    min-height: 600px;
    padding: 40px;
}

.history-section {
    color: #1B1D43;
}

.history-section h2 {
    font-size: 28px;
    font-weight: 600;
    color: #1B1D43;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
}

.history-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2D3561, #1B1D43);
    border-radius: 2px;
}

/* Filter Pane */
.filter-pane {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-pane label {
    font-weight: 600;
    color: #1B1D43;
    margin-bottom: 8px;
    font-size: 14px;
}

.filter-pane input {
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: #ffffff;
    color: #333;
    transition: all 0.3s ease;
}

.filter-pane input:focus {
    outline: none;
    border-color: #2D3561;
    box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
}

/* Filter buttons container */
.filter-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Apply Filter button */
.btn-apply {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(45, 53, 97, 0.3);
}

.btn-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 53, 97, 0.4);
}

/* Clear Filter button */
.btn-clear {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-clear:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

/* Enhanced dropdown styling to match dashboard.html */
.filter-pane select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background: #ffffff;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

.filter-pane select:hover {
    border-color: #2D3561;
    background-color: #f8f9fa;
}

.filter-pane select:focus {
    outline: none;
    border-color: #2D3561;
    box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
    background-color: #ffffff;
}

.filter-pane select option {
    background: #ffffff;
    color: #333;
    padding: 8px;
    font-size: 14px;
}

.filter-pane select option:hover {
    background: #f8f9fa;
}

/* Table Styles */
.table-wrapper {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.history-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.history-table th {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    color: white;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.history-table td {
    padding: 15px;
    border-bottom: 1px solid #e1e5e9;
    color: #333;
    font-size: 14px;
}

.history-table tr:hover {
    background-color: #f8fafc;
}

.history-table tr:last-child td {
    border-bottom: none;
}

/* Action buttons styling */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: center;
}

.btn {
    padding: 4px 8px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 10px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    width: 100%;
    text-align: center;
}

.btn-outline {
    background: transparent;
    border: 1px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* Status and Diagnosis Badges */
.status-badge, .diagnosis-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-uploaded {
    background: #e3f2fd;
    color: #1976d2;
}

.status-analyzed {
    background: #e8f5e8;
    color: #2e7d32;
}

.diagnosis-positive {
    background: #ffebee;
    color: #c62828;
}

.diagnosis-negative {
    background: #e8f5e8;
    color: #2e7d32;
}

.diagnosis-pending {
    background: #fff3e0;
    color: #f57c00;
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    width: 103.5%;
    margin: 0;
    margin-top: 50px;
    position: relative;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 8px;
        flex-wrap: wrap;
    }

    header .logo img {
        width: 50px;
        margin-left: 15px;
        max-height: 40px;
    }

    header nav ul {
        flex-wrap: wrap;
        gap: 10px;
    }

    header nav ul li {
        margin-right: 15px;
    }

    header nav ul li a {
        font-size: 14px;
    }

    body {
        padding: 120px 20px 0 20px;
    }

    .container {
        width: 95%;
    }

    .header h1 {
        font-size: 28px;
    }

    .content {
        padding: 30px 20px;
    }

    .filter-pane {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .table-wrapper {
        padding: 15px;
    }

    footer {
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        padding: 12px 20px;
        font-size: 11px;
        width: 100vw;
    }
}