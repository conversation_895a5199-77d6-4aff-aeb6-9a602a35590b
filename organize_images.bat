@echo off
echo Organizing result images...

REM Create directories first if they don't exist
mkdir "static\results\result_P0005" 2>nul
mkdir "static\results\result_P0009" 2>nul
mkdir "static\results\result_P0020" 2>nul
mkdir "static\results\result_P0021" 2>nul
mkdir "static\results\result_P0025" 2>nul
mkdir "static\results\result_P0044" 2>nul
mkdir "static\results\result_P0052" 2>nul
mkdir "static\results\result_P0076" 2>nul
mkdir "static\results\result_P0092" 2>nul
mkdir "static\results\result_P0093" 2>nul
mkdir "static\results\result_P0095" 2>nul

REM Move Tr-gl_0000 files to P0000 folder
move "static\results\result_UNKNOWN\result_Tr-gl_0000*" "static\results\result_P0000\" 2>nul

REM Move Tr-gl_0003 files to P0003 folder
move "static\results\result_UNKNOWN\result_Tr-gl_0003*" "static\results\result_P0003\" 2>nul

REM Move Tr-pi_0002 files to P0002 folder
move "static\results\result_UNKNOWN\result_Tr-pi_0002*" "static\results\result_P0002\" 2>nul

REM Move other Tr-gl files based on their numbers
move "static\results\result_UNKNOWN\result_Tr-gl_0005*" "static\results\result_P0005\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0009*" "static\results\result_P0009\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0020*" "static\results\result_P0020\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0021*" "static\results\result_P0021\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0025*" "static\results\result_P0025\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0044*" "static\results\result_P0044\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0052*" "static\results\result_P0052\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0076*" "static\results\result_P0076\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0092*" "static\results\result_P0092\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0093*" "static\results\result_P0093\" 2>nul
move "static\results\result_UNKNOWN\result_Tr-gl_0095*" "static\results\result_P0095\" 2>nul

echo Done organizing images!
pause
