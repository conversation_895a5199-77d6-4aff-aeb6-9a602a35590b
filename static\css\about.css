/* General Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Smooth scrolling for anchor links */
html {
    scroll-behavior: smooth;
}

/* Body and Background */
body {
    font-family: 'Helvetica Neue', sans-serif;
    background-color: #fff; /* Light off-white background */
    height: 100vh; /* Full viewport height */
    display: flex;
    flex-direction: column;
    width: 100%; /* Make body take full width */
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #fff; /* Light color background */
    width: 100%;
    z-index: 1000;
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
}

header nav ul {
    display: flex;
    list-style-type: none;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

/* Active navigation state */
header nav ul li a.active {
    font-weight: bold;
    color: #007BFF;
}

/* Log In Button */
header nav ul li .login-btn {
    background-color: #007BFF;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
}

header nav ul li .login-btn:hover {
    background-color: #0056b3;
}


/* Landing Section */
.landing-section {
    position: relative;
    width: 100%;
    height: 100vh; /* Full height of the viewport */
}

.landing-image {
    position: relative;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.landing-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Title Box for Landing Section */
.landing-title-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
    border-radius: 10px;
}

.landing-title-box h1 {
    font-size: 50px;
    font-weight: bold;
}

.landing-title-box p {
    font-size: 18px;
    margin-top: 10px;
}

/* About Us Section */
.about-section {
    background-color: #1B1D43;
    color: white;
    padding: 80px 40px;
    text-align: center;
}

.about-container {
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 20px;
}

.about-description {
    font-size: 18px;
    line-height: 1.6;
    margin: 50px;
}

/* Mission Section */
.mission-section {
    background-color: #fff;
    color: #1B1D43;
    padding: 80px 40px;
    text-align: center;
}

.mission-container {
    max-width: 1200px;
    margin: 0 auto;
}

.mission-description {
    font-size: 18px;
    line-height: 1.6;
    margin-top: 20px;
    margin: 50px;
}

/* Vision Section */
.vision-section {
    background-color: #1B1D43;
    color: white;
    padding: 80px 40px;
    text-align: center;
}

.vision-container {
    max-width: 1200px;
    margin: 0 auto;
}

.vision-description {
    font-size: 18px;
    line-height: 1.6;
    margin-top: 20px;
    margin: 50px;
}

/* Contact Us Section */
.contact-us-section {
    background-color: #fff;
    color: #1B1D43;
    padding: 50px 20px;
    text-align: center;
}

.contact-us-section a {
    color: #1B1D43;
    text-decoration: none;
    font-weight: bold;
}

.contact-form {
    max-width: 600px;
    margin: 20px auto;
    display: flex;
    flex-direction: column;
}

.contact-form label {
    font-size: 16px;
    margin-bottom: 5px;
    margin-top: 20px;
}

.contact-form input, .contact-form textarea {
    padding: 10px;
    margin-bottom: 20px;
    font-size: 16px;
    border: 1px solid #ccc;
    border-radius: 5px;
}

.contact-form button {
    background-color: #1B1D43;
    color: white;
    padding: 15px 30px;
    border: none;
    font-size: 16px;
    cursor: pointer;
    border-radius: 5px;
}

.contact-form button:hover {
    background-color: #007bff;
}

/* Flash Messages */
.flash-message {
    padding: 12px 20px;
    margin: 20px auto;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    max-width: 600px;
}

.flash-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.flash-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-title {
        font-size: 32px;
    }

    .about-description, .mission-description, .vision-description {
        font-size: 16px;
    }

    .contact-form {
        width: 90%;
    }

    .contact-form input, .contact-form textarea {
        font-size: 14px;
    }

    .contact-form button {
        font-size: 14px;
    }
}

footer {
    text-align: center;
    padding: 15px;
    font-size: 12px;
    background-color: #1B1D43;
    border-top: 1px solid #ccc;
    color: #fff;
}

footer a {
    color: #fff; /* Make the Privacy Policy link white */
    text-decoration: underline; /* Remove the underline */
}
