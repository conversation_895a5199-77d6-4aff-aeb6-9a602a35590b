* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 50%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.content {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
    min-height: 600px;
    animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.register-section {
    flex: 1;
    max-width: 600px;
    padding: 60px;
    background: #ffffff;
    text-align: center;
}

.register-section h2 {
    margin-bottom: 15px;
    font-size: 28px;
    font-weight: 600;
    color: #1B1D43;
    position: relative;
    letter-spacing: 0.3px;
}

.register-section h2::before {
    content: '👤';
    display: block;
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.8;
}

.register-section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2D3561, #1B1D43);
    border-radius: 2px;
}

.register-subtitle {
    color: #666;
    margin-bottom: 40px;
    font-size: 16px;
    line-height: 1.5;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 25px;
    text-align: left;
    flex: 1;
}

.form-group.full-width {
    flex: 1 1 100%;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #1B1D43;
    font-size: 14px;
    text-transform: capitalize;
    letter-spacing: 0.2px;
}

.form-group input {
    width: 100%;
    padding: 15px 18px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: #ffffff;
    color: #333;
    font-weight: 400;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #2D3561;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
    transform: translateY(-1px);
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 14px;
    color: #666;
    font-weight: 500;
    user-select: none;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #2D3561;
}

.form-actions {
    margin-top: 30px;
    text-align: center;
}

.register-btn {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    color: white;
    border: none;
    padding: 16px 40px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(45, 53, 97, 0.3);
    width: 100%;
    margin-bottom: 20px;
}

.register-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(45, 53, 97, 0.4);
    background: linear-gradient(135deg, #1B1D43 0%, #2D3561 100%);
}

.login-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.login-section p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.login-link {
    color: #2D3561;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.login-link:hover {
    color: #1B1D43;
    text-decoration: underline;
}

/* Flash Messages */
.flash-message {
    padding: 12px 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}

.flash-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.flash-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    width: 103.5%;
    margin: 0;
    margin-top: 50px;
    position: relative;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .register-section {
        padding: 40px 30px;
    }
    
    .register-section h2 {
        font-size: 24px;
    }
    
    .form-group input {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .register-btn {
        padding: 14px 30px;
        font-size: 14px;
    }
}