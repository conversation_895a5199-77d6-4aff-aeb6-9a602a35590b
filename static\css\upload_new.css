/* Full image modal preview */
/* Gallery container box (hidden by default) */
#image-preview-container {
    min-height: 0;
}
.image-gallery-box {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    background: #f8fafc;
    border: 2px solid #20505c;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.08);
    padding: 24px 18px 18px 18px;
    margin: 24px 0 0 0;
    transition: box-shadow 0.2s, border-color 0.2s;
    cursor: pointer;
    min-height: 120px;
    justify-content: center;
    align-items: center;
    position: relative;
    animation: fadeIn 0.5s;
}
.image-gallery-box:hover {
    box-shadow: 0 8px 32px rgba(0,123,255,0.18);
    border-color: #0056b3;
}
.patient-preview-img {
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    border: 2px solid #e1e5e9;
    margin: 0;
    pointer-events: none; /* Prevent click on individual images */
}

/* Modal for gallery */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.88);
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.image-modal-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
    max-width: 90vw;
    max-height: 80vh;
    overflow-y: auto;
    justify-content: center;
    align-items: center;
    padding: 24px 12px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
}
.gallery-modal-img {
    width: 180px;
    height: 180px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.13);
    border: 2px solid #e1e5e9;
    background: #f8fafc;
}
.image-modal-close {
    position: absolute;
    top: 30px;
    right: 50px;
    color: #fff;
    font-size: 48px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1010;
    transition: color 0.2s;
}
.image-modal-close:hover {
    color: #ff6b6b;
}
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
/* Image preview container under upload box */
#image-preview-container {
    margin-top: 18px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    min-height: 40px;
}
.patient-preview-img {
    max-width: 90px;
    max-height: 90px;
    border: 1.5px solid #ccc;
    border-radius: 6px;
    object-fit: cover;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    margin: 2px;
    transition: transform 0.2s;
}
.patient-preview-img:hover {
    transform: scale(1.08);
    border-color: #16A085;
}
/* Clear Folder button style in upload box */
.clear-folder-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 16px;
    border-radius: 4px;
    margin-top: 15px;
    cursor: pointer;
    font-size: 15px;
    transition: background 0.2s, color 0.2s;
}
.clear-folder-btn:hover {
    background: rgba(255,255,255,0.35);
    color: #007BFF;
}

/* Patient images preview styling */
#patient-images-preview {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
}
.patient-preview-img {
    max-width: 80px;
    max-height: 80px;
    margin: 2px;
    border: 1px solid #ccc;
    border-radius: 4px;
    object-fit: cover;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
}
.no-images-msg {
    color: #ccc;
    font-size: 14px;
    margin-top: 10px;
    width: 100%;
    text-align: center;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', sans-serif;
}

/* Header */
header {
    display: flex;
    position: fixed;
    justify-content: space-between;
    align-items: center;
    font-family: 'Helvetica Neue', sans-serif;
    padding: 10px;
    background-color: #fff;
    width: 100%;
    z-index: 10;
    top: 0;
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

header .logo img {
    width: 60px;
    height: auto;
    margin-left: 30px;
    object-fit: contain;
    max-height: 50px;
}

header nav ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
}

header nav ul li {
    margin-right: 20px;
}

header nav ul li a {
    text-decoration: none;
    color: #1B1D43;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
}

header nav ul li a:hover {
    color: #007BFF;
    text-decoration: underline;
}

body {
    background: linear-gradient(135deg, #2D3561 0%, #1B1D43 100%);
    min-height: 100vh;
    color: #fff;
    padding: 100px 20px 0 20px;
    display: flex;
    flex-direction: column;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    flex: 1;
    margin-bottom: 20px;
    width: 80%;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 36px;
    font-weight: 700;
    color: #fff;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.content {
    display: flex;
    gap: 0;
    background: #ffffff;
    border-radius: 24px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    position: relative;
}

.left-section {
    flex: 1;
    padding: 50px;
    background: #ffffff;
}

.right-section {
    flex: 1;
    padding: 50px;
    background: #f8fafc;
    text-align: center;
}

.left-section h2,
.right-section h2 {
    margin-bottom: 30px;
    font-size: 24px;
    color: #2D3748;
    text-align: center;
    font-weight: 700;
    position: relative;
    letter-spacing: 0.3px;
}

.left-section h2::after,
.right-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #2D3561, #1B1D43);
    border-radius: 2px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #4A5568;
    font-size: 14px;
    margin-bottom: 8px;
    text-transform: capitalize;
    letter-spacing: 0.2px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid #E2E8F0;
    border-radius: 8px;
    font-size: 16px;
    background: #F8FAFC;
    color: #2D3748;
    font-weight: 400;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #2D3561;
    background: white;
    box-shadow: 0 0 0 3px rgba(45, 53, 97, 0.1);
    transform: translateY(-1px);
}

/* Readonly age field styling */
.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #495057;
    cursor: not-allowed;
    border-color: #ced4da;
}

.form-group input[readonly]:focus {
    background-color: #f8f9fa;
    box-shadow: none;
    transform: none;
    border-color: #ced4da;
}

/* Radiologist field with Dr. prefix styling */
#radiologist {
    font-family: 'Helvetica Neue', sans-serif;
    letter-spacing: 0.3px;
}

#radiologist::selection {
    background-color: rgba(45, 53, 97, 0.2);
}

.upload-box {
    background: linear-gradient(135deg, #16A085 0%, #1ABC9C 100%);
    color: white;
    padding: 60px 40px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.4s ease;
    min-height: 280px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    border: 3px dashed rgba(255, 255, 255, 0.4);
    box-shadow: 0 12px 40px rgba(22, 160, 133, 0.25);
    margin-bottom: 20px;
}

.upload-box:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 50px rgba(22, 160, 133, 0.35);
    border-color: rgba(255, 255, 255, 0.7);
    background: linear-gradient(135deg, #17A2B8 0%, #20C997 100%);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 20px;
    animation: float 3s ease-in-out infinite;
}

.upload-text {
    text-align: center;
}

.upload-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.upload-subtitle {
    font-size: 14px;
    opacity: 0.9;
    line-height: 1.4;
}

.info-icon {
    font-size: 24px;
    margin-top: 15px;
    opacity: 0.8;
}



.footer {
    text-align: center;
    margin-top: 30px;
}

.save-btn {
    background: linear-gradient(135deg, #6C757D 0%, #495057 100%);
    color: white;
    padding: 18px 32px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: 100%;
    margin-top: 20px;
}

.save-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
    background: linear-gradient(135deg, #5A6268 0%, #343A40 100%);
}

.save-btn:disabled {
    background: #cccccc !important;
    color: #666666 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Verification button states */
.save-btn.verification-success {
    background: #28a745 !important;
    color: white !important;
    border-color: #28a745 !important;
}

.save-btn.verification-failed {
    background: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
    cursor: not-allowed !important;
}

.save-btn.verification-success:hover:not(:disabled) {
    background: #218838 !important;
    border-color: #1e7e34 !important;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4) !important;
}

.save-btn.verification-failed:hover {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    box-shadow: none !important;
    transform: none !important;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Footer */
footer {
    text-align: center;
    padding: 15px 20px;
    font-size: 12px;
    background-color: #fff;
    border-top: 1px solid #ccc;
    color: #1B1D43;
    width: 103.9%;
    margin: 0;
    margin-top: 50px;
    position: relative;
    left: 50%;
    right: 90%;
    margin-left: -50vw;
    margin-right: -30vw;
}

footer a {
    color: #1B1D43;
    text-decoration: underline;
    margin-left: 10px;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #007BFF;
}

/* Responsive Design */
@media (max-width: 768px) {
    header {
        padding: 8px;
        flex-wrap: wrap;
    }

    header .logo img {
        width: 50px;
        margin-left: 15px;
        max-height: 40px;
    }

    header nav ul {
        flex-wrap: wrap;
        gap: 10px;
    }

    header nav ul li {
        margin-right: 15px;
    }

    header nav ul li a {
        font-size: 14px;
    }

    body {
        padding: 120px 20px 0 20px;
    }

    .content {
        flex-direction: column;
    }

    .left-section,
    .right-section {
        padding: 30px 25px;
    }

    .upload-box {
        padding: 40px 20px;
        min-height: 200px;
    }

    .save-btn {
        min-width: auto;
        width: 100%;
    }

    footer {
        left: 50%;
        right: 50%;
        margin-left: -50vw;
        margin-right: -50vw;
        padding: 12px 20px;
        font-size: 11px;
        width: 100vw;
    }
}