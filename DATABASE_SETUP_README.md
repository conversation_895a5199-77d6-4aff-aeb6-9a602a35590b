# Radiolens Database Setup Guide

This guide explains how to set up the MySQL database for storing image analysis results in the Radiolens application.

## Overview

The updated `app.py` now includes functionality to save image analysis results to a MySQL database table called `images_result` under the `Patient_Info` database.

## Database Schema

### images_result Table Structure

| Field | Type | Description |
|-------|------|-------------|
| `patient_id` | VARCHAR(50) PRIMARY KEY | Patient ID (Primary Key) |
| `patient_name` | VARCHAR(100) | Pat<PERSON>'s full name |
| `result_image` | VARCHAR(255) | Filename of the result image |
| `predicted_class` | VARCHAR(100) | AI prediction (e.g., "No tumor", "Tumor detected") |
| `confidence` | DECIMAL(5,4) | Confidence score (0.0000 to 1.0000) |
| `x1` | INT | Bounding box top-left X coordinate |
| `y1` | INT | Bounding box top-left Y coordinate |
| `x2` | INT | Bounding box bottom-right X coordinate |
| `y2` | INT | Bounding box bottom-right Y coordinate |
| `width` | INT | Bounding box width |
| `height` | INT | Bounding box height |
| `mask_area` | INT | Area of the detected region |
| `location` | VARCHAR(255) | Reserved field for future location data |
| `created_at` | TIMESTAMP | Record creation timestamp |

## Setup Instructions

### Step 1: Install Python Dependencies

Install the required Python packages:

```bash
pip install -r requirements.txt
```

Or install individually:
```bash
pip install mysql-connector-python==8.1.0
pip install Flask==2.3.3
pip install ultralytics==8.0.196
pip install opencv-python==********
```

### Step 2: Create Database Table

Choose one of the following methods:

#### Method A: Using PHP Script (Recommended)
1. Open your browser
2. Navigate to `http://localhost/radiolens/create_images_result_table.php`
3. The script will create the table and show the structure

#### Method B: Using SQL Script
1. Open phpMyAdmin
2. Select the `Patient_Info` database
3. Go to SQL tab
4. Copy and paste the contents of `create_images_result_table.sql`
5. Execute the query

#### Method C: Automatic Creation
The table will be created automatically when you run the Flask app for the first time.

### Step 3: Test Database Connection

Run the test script to verify everything is working:

```bash
python test_database_connection.py
```

This will:
- Test database connection
- Verify table structure
- Test data insertion and retrieval
- Clean up test data

### Step 4: Run the Application

Start the Flask application:

```bash
python app.py
```

## How It Works

1. **Image Upload**: User uploads an image through the web interface
2. **AI Analysis**: The YOLO model processes the image and detects tumors
3. **Result Generation**: The system creates a result image with bounding boxes
4. **Database Storage**: All analysis results are automatically saved to the database
5. **Display**: Results are shown to the user on the Report page

## Database Integration Details

### Data Saved to Database

When an image is analyzed, the following data is saved:

```python
result_data = (
    patient_id,        # "P001" (Patient ID - Primary Key)
    patient_name,      # "John Doe" (Patient Name)
    result_image,      # "result_filename.jpg"
    predicted_class,   # "No tumor" or detected class
    confidence,        # 0.8567 (confidence score)
    x1, y1, x2, y2,   # Bounding box coordinates
    width, height,     # Bounding box dimensions
    mask_area,         # Area of detected region
    None              # location (reserved for future use)
)
```

### Database Configuration

The database connection settings are in `app.py`:

```python
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '',
    'database': 'Patient_Info'
}
```

Modify these settings if your MySQL setup is different.

## Troubleshooting

### Common Issues

1. **MySQL Connection Error**
   - Ensure XAMPP MySQL service is running
   - Check database credentials in `app.py`
   - Verify `Patient_Info` database exists

2. **Table Not Found Error**
   - Run `create_images_result_table.php` to create the table
   - Check if you're connected to the correct database

3. **Import Error for mysql.connector**
   - Install the package: `pip install mysql-connector-python`

4. **Permission Denied**
   - Check MySQL user permissions
   - Ensure the user has INSERT, SELECT privileges on `Patient_Info` database

### Testing Commands

```bash
# Test database connection
python test_database_connection.py

# Check if table exists (in MySQL command line)
USE Patient_Info;
SHOW TABLES LIKE 'images_result';
DESCRIBE images_result;

# View saved results
SELECT * FROM images_result ORDER BY created_at DESC LIMIT 10;
```

## Future Enhancements

The `location` field has been added for future use. You can update this field to store:
- Hospital/clinic location
- Patient location
- Image capture location
- Any other location-related data

## File Structure

```
Radiolens/
├── app.py                           # Main Flask application (updated)
├── requirements.txt                 # Python dependencies
├── create_images_result_table.sql   # SQL script to create table
├── create_images_result_table.php   # PHP script to create table
├── test_database_connection.py      # Database test script
├── DATABASE_SETUP_README.md         # This file
└── static/
    ├── uploads/                     # Uploaded images
    └── results/                     # Processed result images
```

## Support

If you encounter any issues:
1. Check the console output for error messages
2. Run the test script to diagnose problems
3. Verify your MySQL and XAMPP setup
4. Ensure all dependencies are installed correctly
