<?php
// Database configuration file for RadioLens
// Modify these settings according to your MySQL setup

// Database connection settings
define('DB_HOST', 'localhost');
define('DB_USERNAME', 'root');        // Default XAMPP username
define('DB_PASSWORD', '');            // Default XAMPP password (empty)
define('DB_NAME', 'patient_info');    // Your manually created database name

// File upload settings
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB per file
define('ALLOWED_FILE_TYPES', [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'application/dicom',
    'application/pdf'
]);

define('UPLOAD_DIR', 'uploads/');

// Error reporting (set to false in production)
define('DEBUG_MODE', true);

if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// Function to get database connection
function getDBConnection() {
    try {
        $conn = new mysqli(DB_HOST, DB_USERNAME, DB_PASSWORD, DB_NAME);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        // Set charset to utf8
        $conn->set_charset("utf8");
        
        return $conn;
    } catch (Exception $e) {
        if (DEBUG_MODE) {
            die("Database connection error: " . $e->getMessage());
        } else {
            die("Database connection error. Please contact administrator.");
        }
    }
}

// Function to validate file type
function isValidFileType($fileType) {
    return in_array($fileType, ALLOWED_FILE_TYPES);
}

// Function to validate file size
function isValidFileSize($fileSize) {
    return $fileSize <= MAX_FILE_SIZE && $fileSize > 0;
}

// Function to sanitize filename
function sanitizeFilename($filename) {
    // Remove any path information
    $filename = basename($filename);
    
    // Remove special characters except dots, hyphens, and underscores
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    
    // Limit filename length
    if (strlen($filename) > 100) {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $name = pathinfo($filename, PATHINFO_FILENAME);
        $filename = substr($name, 0, 95) . '.' . $extension;
    }
    
    return $filename;
}

// Function to generate unique filename
function generateUniqueFilename($originalFilename) {
    $extension = pathinfo($originalFilename, PATHINFO_EXTENSION);
    $sanitizedName = sanitizeFilename(pathinfo($originalFilename, PATHINFO_FILENAME));
    return $sanitizedName . '_' . uniqid() . '_' . time() . '.' . $extension;
}

// Function to format file size
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// Function to log errors (you can extend this to write to a file)
function logError($message, $context = []) {
    if (DEBUG_MODE) {
        error_log("RadioLens Error: " . $message . " Context: " . json_encode($context));
    }
}
?>
