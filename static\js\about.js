document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation links except login button
    const navLinks = document.querySelectorAll('header nav ul li a:not(.login-btn)');

    // Function to remove active class from all nav links
    function removeActiveClasses() {
        navLinks.forEach(link => {
            link.classList.remove('active');
        });
    }

    // Add click event listeners to navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Remove active class from all links first
            removeActiveClasses();

            // Add active class to clicked link
            this.classList.add('active');

            // Handle Contact Us link - smooth scroll to section
            if (this.getAttribute('href') === '#contact-us-section') {
                e.preventDefault();
                const contactSection = document.getElementById('contact-us-section');
                if (contactSection) {
                    contactSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
            // Handle About Us link - scroll to top of page
            else if (this.getAttribute('href').includes('about')) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Set initial active state based on current page
    const currentPath = window.location.pathname;
    if (currentPath.includes('/about')) {
        const aboutLink = document.querySelector('a[href*="about"]');
        if (aboutLink) {
            aboutLink.classList.add('active');
        }
    }
});
