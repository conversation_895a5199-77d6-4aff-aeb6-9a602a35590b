<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RadioLens Login</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/Login.css') }}">
</head>

<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <img src="{{ url_for('static', filename='images/RadioLens_Logo.png') }}" alt="Logo">
        </div>
        <nav>
            <ul>
                <li><a href="{{ url_for('home') }}">Home</a></li>
                <li><a href="{{ url_for('about') }}">About Us</a></li>
                <li><a href="{{ url_for('about') }}#contact-us-section">Contact Us</a></li>
            </ul>
        </nav>
    </header>

    <div class="container">
        <div class="header">
            <h1>Welcome to RadioLens</h1>
        </div>

        <div class="content">
            <div class="login-section">
                <h2>Login to Your Account</h2>
                <p class="login-subtitle">Access your medical imaging analysis</p>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="flash-message flash-{{ category }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form id="loginForm" method="post" action="{{ url_for('login') }}">
                    <div class="form-group">
                        <label for="username">User ID:</label>
                        <input type="text" id="username" name="username" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password:</label>
                        <div class="password-container">
                            <input type="password" id="password" name="password" required>
                            <span id="togglePassword" class="password-toggle">Show</span>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="login-btn">Login</button>
                    </div>

                    <div class="signup-section">
                        <p>Don't have an account? <a href="{{ url_for('register') }}" class="signup-link">Sign Up Now</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <footer>
        <p>©2025 RadioLens. All rights reserved.</p>
        <a href="">Privacy Policy</a>
    </footer>


    <!-- Login page script moved to static/js/login.js -->
    <script src="{{ url_for('static', filename='js/Login.js') }}"></script>

    <!-- Navigation highlighting script -->
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
</body>
</html>
