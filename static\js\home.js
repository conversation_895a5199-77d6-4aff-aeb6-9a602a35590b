// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Make all product items visible when they come into view
    const productItems = document.querySelectorAll('.product-item');
    
    // Set initial opacity to 1 for all product items
    productItems.forEach(item => {
        item.style.opacity = 1;
    });
    
    // Add smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('header nav ul li a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Only apply smooth scroll for hash links
            if (this.hash !== '') {
                e.preventDefault();
                
                const hash = this.hash;
                
                // Smooth scroll to the target
                document.querySelector(hash).scrollIntoView({
                    behavior: 'smooth'
                });
                
                // Update URL
                window.history.pushState(null, null, hash);
            }
        });
    });
});
