"""
Google Drive Configuration for Radiolens

This file contains configuration settings for Google Drive integration.
"""

import os

# Google Drive API Configuration
# Temporarily disabled due to storage quota issues
GOOGLE_DRIVE_ENABLED = os.getenv('GOOGLE_DRIVE_ENABLED', 'False').lower() == 'true'
SERVICE_ACCOUNT_FILE = os.getenv('GOOGLE_DRIVE_CREDENTIALS', 'credentials/service_account.json')
RADIOLENS_ROOT_FOLDER_NAME = 'Radiolens'

# Folder structure in Google Drive
PATIENT_FOLDER_PREFIX = 'Patient_'
RESULTS_FOLDER_NAME = 'Results'
ORIGINAL_IMAGES_FOLDER_NAME = 'Original_Images'

# File upload settings
MAX_FILE_SIZE_MB = 100  # Maximum file size in MB
ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.dcm']

# Shared Drive Configuration (manually added)
# Set to False to use regular shared folder instead of shared drive
USE_SHARED_DRIVE = False
SHARED_DRIVE_ID = None  # Not needed for regular shared folder

# Cache settings for downloaded images
CACHE_ENABLED = True
CACHE_DIRECTORY = 'cache/google_drive'
CACHE_EXPIRY_HOURS = 24

# Error handling settings
MAX_RETRY_ATTEMPTS = 3
RETRY_DELAY_SECONDS = 2

# Local fallback settings
USE_LOCAL_FALLBACK = True
LOCAL_UPLOAD_FOLDER = 'static/uploads'
LOCAL_RESULT_FOLDER = 'static/results'
